package org.plugin.messagetools.paper.service;

import org.bukkit.entity.Player;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.plugin.java.JavaPlugin;
import org.plugin.messagetools.paper.config.ConfigManager;
import org.plugin.messagetools.paper.util.DeathCauseAnalyzer;

import java.nio.charset.StandardCharsets;

/**
 * 死亡消息服务
 * 负责处理玩家死亡事件并发送到Velocity代理
 */
public class DeathMessageService {
    
    private final JavaPlugin plugin;
    private final ConfigManager configManager;
    private final DeathCauseAnalyzer deathCauseAnalyzer;
    
    // 插件消息通道
    private static final String DEATH_MESSAGE_CHANNEL = "messagetools:death";
    
    public DeathMessageService(JavaPlugin plugin, ConfigManager configManager) {
        this.plugin = plugin;
        this.configManager = configManager;
        this.deathCauseAnalyzer = new DeathCauseAnalyzer();
    }
    
    /**
     * 处理玩家死亡事件
     */
    public void handlePlayerDeath(PlayerDeathEvent event) {
        Player player = event.getEntity();
        
        boolean debugEnabled = configManager.getBoolean("debug.enabled", false);
        boolean verboseEvents = configManager.getBoolean("debug.verbose_events", false);
        
        if (debugEnabled) {
            plugin.getLogger().info("处理玩家死亡事件: " + player.getName());
        }
        
        // 检查死亡消息功能是否启用
        if (!configManager.getBoolean("death_messages.enabled", true)) {
            if (debugEnabled && verboseEvents) {
                plugin.getLogger().info("死亡消息功能已禁用，跳过处理");
            }
            return;
        }
        
        // 分析死亡原因
        String deathCause = deathCauseAnalyzer.analyzeDeath(event);
        String weapon = deathCauseAnalyzer.getWeaponName(event);
        
        if (debugEnabled && verboseEvents) {
            plugin.getLogger().info("死亡原因: " + deathCause);
            plugin.getLogger().info("武器: " + (weapon != null ? weapon : "无"));
        }
        
        // 检查是否应该取消原始死亡消息
        if (configManager.getBoolean("death_messages.cancel_original", true)) {
            event.setDeathMessage(null);
            if (debugEnabled && verboseEvents) {
                plugin.getLogger().info("已取消原始死亡消息");
            }
        }
        
        // 发送死亡消息到Velocity代理
        sendDeathMessageToProxy(player.getName(), deathCause, weapon);
    }
    
    /**
     * 发送死亡消息到Velocity代理
     */
    private void sendDeathMessageToProxy(String playerName, String deathCause, String weapon) {
        boolean debugEnabled = configManager.getBoolean("debug.enabled", false);
        boolean verboseEvents = configManager.getBoolean("debug.verbose_events", false);
        
        try {
            // 构建消息数据 (格式: playerName|deathCause|weapon)
            String messageData = playerName + "|" + deathCause + "|" + (weapon != null ? weapon : "");
            byte[] data = messageData.getBytes(StandardCharsets.UTF_8);
            
            if (debugEnabled && verboseEvents) {
                plugin.getLogger().info("发送插件消息: " + messageData);
            }
            
            // 发送插件消息到代理
            plugin.getServer().sendPluginMessage(plugin, DEATH_MESSAGE_CHANNEL, data);
            
            if (debugEnabled) {
                plugin.getLogger().info("死亡消息已发送到代理: " + playerName);
            }
            
        } catch (Exception e) {
            plugin.getLogger().severe("发送死亡消息到代理时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 检查死亡消息功能是否启用
     */
    public boolean isEnabled() {
        return configManager.getBoolean("death_messages.enabled", true);
    }
    
    /**
     * 获取死亡消息通道
     */
    public static String getDeathMessageChannel() {
        return DEATH_MESSAGE_CHANNEL;
    }
}
