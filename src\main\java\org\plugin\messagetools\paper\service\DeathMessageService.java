package org.plugin.messagetools.paper.service;

import org.bukkit.Material;
import org.bukkit.entity.Arrow;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.java.JavaPlugin;
import org.plugin.messagetools.paper.config.ConfigManager;
import org.plugin.messagetools.paper.util.DeathCauseAnalyzer;
import org.plugin.messagetools.paper.util.MineDownUtil;

import java.nio.charset.StandardCharsets;

/**
 * 死亡消息服务
 * 负责处理玩家死亡事件并发送到Velocity代理
 */
public class DeathMessageService {

    private final JavaPlugin plugin;
    private final ConfigManager configManager;
    private final DeathCauseAnalyzer deathCauseAnalyzer;

    // 插件消息通道
    private static final String DEATH_MESSAGE_CHANNEL = "messagetools:death";

    // 防重复发送机制
    private final java.util.Set<String> processedDeaths = java.util.concurrent.ConcurrentHashMap.newKeySet();
    
    public DeathMessageService(JavaPlugin plugin, ConfigManager configManager) {
        this.plugin = plugin;
        this.configManager = configManager;
        this.deathCauseAnalyzer = new DeathCauseAnalyzer();
    }
    
    /**
     * 处理玩家死亡事件
     */
    public void handlePlayerDeath(PlayerDeathEvent event) {
        Player player = event.getEntity();
        
        boolean debugEnabled = configManager.getBoolean("debug.enabled", false);
        boolean verboseEvents = configManager.getBoolean("debug.verbose_events", false);
        
        // 生成唯一的死亡事件ID（玩家名+时间戳）
        String deathId = player.getName() + "_" + System.currentTimeMillis();

        // 防重复处理机制
        if (!processedDeaths.add(deathId)) {
            if (debugEnabled) {
                plugin.getLogger().info("跳过重复的死亡事件: " + player.getName());
            }
            return;
        }

        // 清理旧的死亡记录（保留最近1000个）
        if (processedDeaths.size() > 1000) {
            processedDeaths.clear();
        }

        if (debugEnabled) {
            plugin.getLogger().info("处理玩家死亡事件: " + player.getName() + " (ID: " + deathId + ")");
        }

        // 检查死亡消息功能是否启用
        if (!configManager.getBoolean("death_messages.enabled", true)) {
            if (debugEnabled && verboseEvents) {
                plugin.getLogger().info("死亡消息功能已禁用，跳过处理");
            }
            return;
        }
        
        // 构建完整的死亡消息（使用原生API，包含悬停效果）
        net.kyori.adventure.text.Component deathMessage = buildCompleteDeathMessage(event);

        if (deathMessage != null) {
            // 将Component序列化为JSON
            String messageJson = net.kyori.adventure.text.serializer.gson.GsonComponentSerializer.gson().serialize(deathMessage);

            if (debugEnabled && verboseEvents) {
                plugin.getLogger().info("构建的死亡消息JSON: " + messageJson);
            }

            // 发送完整的死亡消息到Velocity代理
            sendCompleteDeathMessageToProxy(player.getName(), messageJson);
        } else {
            if (debugEnabled) {
                plugin.getLogger().warning("无法构建死亡消息: " + player.getName());
            }
        }
    }

    /**
     * 构建完整的死亡消息（支持MineDown格式和team前缀）
     */
    private net.kyori.adventure.text.Component buildCompleteDeathMessage(PlayerDeathEvent event) {
        // 获取原始死亡消息
        net.kyori.adventure.text.Component originalMessage = event.deathMessage();

        if (originalMessage != null) {
            // 使用MineDown增强原始死亡消息的颜色效果
            return enhanceDeathMessageWithMineDown(originalMessage, event);
        }

        // 如果没有原始死亡消息，使用MineDown自定义构建
        return buildMineDownDeathMessage(event);
    }

    /**
     * 使用MineDown增强原始死亡消息的颜色效果
     */
    private net.kyori.adventure.text.Component enhanceDeathMessageWithMineDown(net.kyori.adventure.text.Component originalMessage, PlayerDeathEvent event) {
        // 将原始消息转换为纯文本
        String plainText = net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer.plainText().serialize(originalMessage);

        // 分析死亡类型并应用相应的MineDown格式
        Player player = event.getEntity();
        EntityDamageEvent damageEvent = player.getLastDamageCause();

        if (damageEvent instanceof org.bukkit.event.entity.EntityDamageByEntityEvent) {
            org.bukkit.event.entity.EntityDamageByEntityEvent entityEvent =
                (org.bukkit.event.entity.EntityDamageByEntityEvent) damageEvent;
            Entity damager = entityEvent.getDamager();

            if (damager instanceof Player) {
                // PvP死亡 - 使用渐变色
                return applyPvPMineDownFormat(plainText, player, (Player) damager);
            } else if (damager instanceof Arrow) {
                Arrow arrow = (Arrow) damager;
                if (arrow.getShooter() instanceof Player) {
                    // 远程PvP - 使用渐变色
                    return applyPvPMineDownFormat(plainText, player, (Player) arrow.getShooter());
                }
            }
            // 怪物攻击 - 使用特定颜色
            return applyMobMineDownFormat(plainText, damager);
        }

        // 环境死亡 - 根据死亡原因使用不同颜色
        return applyEnvironmentMineDownFormat(plainText, damageEvent);
    }

    /**
     * 构建MineDown格式的自定义死亡消息
     */
    private net.kyori.adventure.text.Component buildMineDownDeathMessage(PlayerDeathEvent event) {
        Player player = event.getEntity();
        EntityDamageEvent damageEvent = player.getLastDamageCause();

        String playerDisplayName = player.getDisplayName();

        if (damageEvent == null) {
            return MineDownUtil.parse("&rainbow&" + playerDisplayName + " &7死了");
        }

        // 根据伤害类型构建消息
        if (damageEvent instanceof org.bukkit.event.entity.EntityDamageByEntityEvent) {
            org.bukkit.event.entity.EntityDamageByEntityEvent entityEvent =
                (org.bukkit.event.entity.EntityDamageByEntityEvent) damageEvent;
            Entity damager = entityEvent.getDamager();

            if (damager instanceof Player) {
                return buildMineDownPvPMessage(player, (Player) damager);
            } else if (damager instanceof Arrow) {
                Arrow arrow = (Arrow) damager;
                if (arrow.getShooter() instanceof Player) {
                    return buildMineDownPvPMessage(player, (Player) arrow.getShooter());
                }
                return MineDownUtil.parse("&#ff6b6b-#ee5a52&" + playerDisplayName + " &7被射死了");
            } else {
                return buildMineDownMobMessage(player, damager);
            }
        }

        // 环境死亡
        return buildMineDownEnvironmentMessage(player, damageEvent);
    }

    /**
     * 处理原始死亡消息，替换玩家名为纯玩家名
     */
    private net.kyori.adventure.text.Component processOriginalDeathMessage(net.kyori.adventure.text.Component originalMessage, PlayerDeathEvent event) {
        Player player = event.getEntity();
        String originalPlayerName = player.getName();
        String plainPlayerName = getPlainPlayerName(player);

        // 如果玩家名相同，直接返回原始消息
        if (originalPlayerName.equals(plainPlayerName)) {
            return originalMessage;
        }

        // 递归处理Component，替换玩家名
        return replacePlayerNameInComponent(originalMessage, originalPlayerName, plainPlayerName, event);
    }

    /**
     * 递归替换Component中的玩家名
     */
    private net.kyori.adventure.text.Component replacePlayerNameInComponent(
            net.kyori.adventure.text.Component component,
            String originalName,
            String plainName,
            PlayerDeathEvent event) {

        if (component == null) {
            return null;
        }

        // 处理文本组件
        if (component instanceof net.kyori.adventure.text.TextComponent) {
            net.kyori.adventure.text.TextComponent textComponent = (net.kyori.adventure.text.TextComponent) component;
            String content = textComponent.content();

            // 替换文本中的玩家名
            if (content.contains(originalName)) {
                content = content.replace(originalName, plainName);
            }

            // 创建新的文本组件
            net.kyori.adventure.text.TextComponent.Builder builder = net.kyori.adventure.text.Component.text()
                .content(content)
                .style(textComponent.style());

            // 递归处理子组件
            for (net.kyori.adventure.text.Component child : textComponent.children()) {
                net.kyori.adventure.text.Component processedChild = replacePlayerNameInComponent(child, originalName, plainName, event);
                if (processedChild != null) {
                    builder.append(processedChild);
                }
            }

            return builder.build();
        }

        // 对于其他类型的组件，如果包含玩家名，则回退到自定义构建
        String componentText = net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer.plainText().serialize(component);
        if (componentText.contains(originalName)) {
            // 如果组件包含原始玩家名，回退到自定义构建
            return buildCustomDeathMessage(event);
        }

        // 否则保持原组件不变
        return component;
    }

    /**
     * 为PvP死亡消息应用MineDown格式
     */
    private net.kyori.adventure.text.Component applyPvPMineDownFormat(String plainText, Player victim, Player killer) {
        String victimName = victim.displayName() != null ?
            net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer.plainText().serialize(victim.displayName()) :
            victim.getName();
        String killerName = killer.displayName() != null ?
            net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer.plainText().serialize(killer.displayName()) :
            killer.getName();

        // 使用渐变色增强PvP消息
        String enhancedText = plainText
            .replace(victim.getName(), "&#ff6b6b-#ee5a52&" + victimName)
            .replace(killer.getName(), "&#4ecdc4-#44a08d&" + killerName);

        return MineDownUtil.parse(enhancedText);
    }

    /**
     * 为怪物攻击死亡消息应用MineDown格式
     */
    private net.kyori.adventure.text.Component applyMobMineDownFormat(String plainText, Entity damager) {
        String mobColor;
        if (damager instanceof org.bukkit.entity.Zombie) {
            mobColor = "&#2d5016&"; // 深绿色
        } else if (damager instanceof org.bukkit.entity.Skeleton) {
            mobColor = "&#f5f5f5&"; // 白色
        } else if (damager instanceof org.bukkit.entity.Creeper) {
            mobColor = "&#4caf50&"; // 绿色
        } else if (damager instanceof org.bukkit.entity.Spider) {
            mobColor = "&#3e2723&"; // 棕色
        } else {
            mobColor = "&#ff5722&"; // 橙红色
        }

        String enhancedText = mobColor + plainText;
        return MineDownUtil.parse(enhancedText);
    }

    /**
     * 为环境死亡消息应用MineDown格式
     */
    private net.kyori.adventure.text.Component applyEnvironmentMineDownFormat(String plainText, EntityDamageEvent damageEvent) {
        String colorCode;
        if (damageEvent != null) {
            switch (damageEvent.getCause()) {
                case FALL:
                    colorCode = "&#ff9a9e-#fecfef&"; // 粉色渐变
                    break;
                case DROWNING:
                    colorCode = "&#a8edea-#fed6e3&"; // 蓝粉渐变
                    break;
                case LAVA:
                case FIRE:
                case FIRE_TICK:
                    colorCode = "&#ff9a56-#ff6b35&"; // 橙色渐变
                    break;
                case ENTITY_EXPLOSION:
                case BLOCK_EXPLOSION:
                    colorCode = "&#ffd93d-#6bcf7f&"; // 黄绿渐变
                    break;
                case VOID:
                    colorCode = "&#667eea-#764ba2&"; // 紫色渐变
                    break;
                default:
                    colorCode = "&rainbow&"; // 彩虹色
                    break;
            }
        } else {
            colorCode = "&rainbow&";
        }

        String enhancedText = colorCode + plainText;
        return MineDownUtil.parse(enhancedText);
    }

    /**
     * 构建MineDown格式的PvP死亡消息
     */
    private net.kyori.adventure.text.Component buildMineDownPvPMessage(Player victim, Player killer) {
        ItemStack weapon = killer.getInventory().getItemInMainHand();

        String victimName = victim.displayName() != null ?
            net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer.plainText().serialize(victim.displayName()) :
            victim.getName();
        String killerName = killer.displayName() != null ?
            net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer.plainText().serialize(killer.displayName()) :
            killer.getName();

        if (weapon != null && weapon.getType() != Material.AIR) {
            String weaponName = weapon.hasItemMeta() && weapon.getItemMeta().hasDisplayName() ?
                net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer.plainText().serialize(weapon.getItemMeta().displayName()) :
                weapon.getType().name();

            return MineDownUtil.parse("&#ff6b6b-#ee5a52&" + victimName +
                                    " &7被 &#4ecdc4-#44a08d&" + killerName +
                                    " &7用 &#ffd93d-#6bcf7f&[" + weaponName + "] &7杀死了");
        } else {
            return MineDownUtil.parse("&#ff6b6b-#ee5a52&" + victimName +
                                    " &7被 &#4ecdc4-#44a08d&" + killerName +
                                    " &7杀死了");
        }
    }

    /**
     * 构建MineDown格式的怪物攻击死亡消息
     */
    private net.kyori.adventure.text.Component buildMineDownMobMessage(Player victim, Entity damager) {
        String victimName = victim.displayName() != null ?
            net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer.plainText().serialize(victim.displayName()) :
            victim.getName();

        String message;
        if (damager instanceof org.bukkit.entity.Zombie) {
            message = "&#ff6b6b-#ee5a52&" + victimName + " &2被僵尸杀死了";
        } else if (damager instanceof org.bukkit.entity.Skeleton) {
            message = "&#ff6b6b-#ee5a52&" + victimName + " &f被骷髅杀死了";
        } else if (damager instanceof org.bukkit.entity.Creeper) {
            message = "&#ff6b6b-#ee5a52&" + victimName + " &a被苦力怕炸死了";
        } else if (damager instanceof org.bukkit.entity.Spider) {
            message = "&#ff6b6b-#ee5a52&" + victimName + " &8被蜘蛛杀死了";
        } else if (damager instanceof org.bukkit.entity.Enderman) {
            message = "&#ff6b6b-#ee5a52&" + victimName + " &5被末影人杀死了";
        } else {
            message = "&#ff6b6b-#ee5a52&" + victimName + " &7被怪物杀死了";
        }

        return MineDownUtil.parse(message);
    }

    /**
     * 构建MineDown格式的环境死亡消息
     */
    private net.kyori.adventure.text.Component buildMineDownEnvironmentMessage(Player victim, EntityDamageEvent damageEvent) {
        String victimName = victim.displayName() != null ?
            net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer.plainText().serialize(victim.displayName()) :
            victim.getName();

        String message;
        if (damageEvent != null) {
            switch (damageEvent.getCause()) {
                case FALL:
                    message = "&#ff9a9e-#fecfef&" + victimName + " &7落地过猛";
                    break;
                case DROWNING:
                    message = "&#a8edea-#fed6e3&" + victimName + " &9淹死了";
                    break;
                case LAVA:
                    message = "&#ff9a56-#ff6b35&" + victimName + " &6试图在熔岩里游泳";
                    break;
                case FIRE:
                case FIRE_TICK:
                    message = "&#ff9a56-#ff6b35&" + victimName + " &6被烧死了";
                    break;
                case SUFFOCATION:
                    message = "&#9e9e9e-#616161&" + victimName + " &7在墙里窒息而亡";
                    break;
                case VOID:
                    message = "&#667eea-#764ba2&" + victimName + " &5掉出了这个世界";
                    break;
                case STARVATION:
                    message = "&#8bc34a-#4caf50&" + victimName + " &a饿死了";
                    break;
                case POISON:
                    message = "&#9c27b0-#673ab7&" + victimName + " &5中毒身亡";
                    break;
                case WITHER:
                    message = "&#424242-#212121&" + victimName + " &8凋零了";
                    break;
                case LIGHTNING:
                    message = "&#ffeb3b-#ffc107&" + victimName + " &e被闪电击中";
                    break;
                case ENTITY_EXPLOSION:
                case BLOCK_EXPLOSION:
                    message = "&#ffd93d-#6bcf7f&" + victimName + " &e爆炸了";
                    break;
                default:
                    message = "&rainbow&" + victimName + " &7死了";
                    break;
            }
        } else {
            message = "&rainbow&" + victimName + " &7死了";
        }

        return MineDownUtil.parse(message);
    }

    /**
     * 手动构建自定义死亡消息（使用纯玩家名）
     */
    private net.kyori.adventure.text.Component buildCustomDeathMessage(PlayerDeathEvent event) {
        Player player = event.getEntity();
        EntityDamageEvent damageEvent = player.getLastDamageCause();

        String playerName = getPlainPlayerName(player);

        if (damageEvent == null) {
            return net.kyori.adventure.text.Component.text(playerName + " 死了");
        }

        // 根据伤害类型构建消息
        if (damageEvent instanceof org.bukkit.event.entity.EntityDamageByEntityEvent) {
            org.bukkit.event.entity.EntityDamageByEntityEvent entityEvent =
                (org.bukkit.event.entity.EntityDamageByEntityEvent) damageEvent;
            Entity damager = entityEvent.getDamager();

            if (damager instanceof Player) {
                return buildPvPDeathMessage(player, (Player) damager);
            } else if (damager instanceof Arrow) {
                Arrow arrow = (Arrow) damager;
                if (arrow.getShooter() instanceof Player) {
                    return buildPvPRangedDeathMessage(player, (Player) arrow.getShooter());
                }
                // 骷髅等怪物射箭
                return net.kyori.adventure.text.Component.text(playerName + " 被射死了");
            } else if (damager instanceof org.bukkit.entity.Zombie) {
                return net.kyori.adventure.text.Component.text(playerName + " 被僵尸杀死了");
            } else if (damager instanceof org.bukkit.entity.Skeleton) {
                return net.kyori.adventure.text.Component.text(playerName + " 被骷髅杀死了");
            } else if (damager instanceof org.bukkit.entity.Creeper) {
                return net.kyori.adventure.text.Component.text(playerName + " 被苦力怕炸死了");
            } else {
                return net.kyori.adventure.text.Component.text(playerName + " 被怪物杀死了");
            }
        }

        // 根据伤害原因构建环境死亡消息
        switch (damageEvent.getCause()) {
            case FALL:
                return net.kyori.adventure.text.Component.text(playerName + " 落地过猛");
            case DROWNING:
                return net.kyori.adventure.text.Component.text(playerName + " 淹死了");
            case LAVA:
                return net.kyori.adventure.text.Component.text(playerName + " 试图在熔岩里游泳");
            case FIRE:
            case FIRE_TICK:
                return net.kyori.adventure.text.Component.text(playerName + " 被烧死了");
            case SUFFOCATION:
                return net.kyori.adventure.text.Component.text(playerName + " 在墙里窒息而亡");
            case VOID:
                return net.kyori.adventure.text.Component.text(playerName + " 掉出了这个世界");
            case STARVATION:
                return net.kyori.adventure.text.Component.text(playerName + " 饿死了");
            case POISON:
                return net.kyori.adventure.text.Component.text(playerName + " 中毒身亡");
            case WITHER:
                return net.kyori.adventure.text.Component.text(playerName + " 凋零了");
            case LIGHTNING:
                return net.kyori.adventure.text.Component.text(playerName + " 被闪电击中");
            case ENTITY_EXPLOSION:
            case BLOCK_EXPLOSION:
                return net.kyori.adventure.text.Component.text(playerName + " 爆炸了");
            default:
                return net.kyori.adventure.text.Component.text(playerName + " 死了");
        }
    }

    /**
     * 构建PvP死亡消息（近战）
     */
    private net.kyori.adventure.text.Component buildPvPDeathMessage(Player victim, Player killer) {
        ItemStack weapon = killer.getInventory().getItemInMainHand();

        // 获取纯玩家名（不包含team前缀等）
        String victimName = getPlainPlayerName(victim);
        String killerName = getPlainPlayerName(killer);

        if (weapon != null && weapon.getType() != Material.AIR) {
            // 有武器的PvP
            net.kyori.adventure.text.Component weaponComponent;

            if (weapon.hasItemMeta() && weapon.getItemMeta().hasDisplayName()) {
                // 使用武器的显示名称（保持原生悬停效果）
                weaponComponent = weapon.getItemMeta().displayName();
            } else {
                // 使用材料名称
                weaponComponent = net.kyori.adventure.text.Component.text(weapon.getType().name());
            }

            return net.kyori.adventure.text.Component.text()
                .append(net.kyori.adventure.text.Component.text(victimName))
                .append(net.kyori.adventure.text.Component.text(" 被 "))
                .append(net.kyori.adventure.text.Component.text(killerName))
                .append(net.kyori.adventure.text.Component.text(" 用 "))
                .append(weaponComponent)
                .append(net.kyori.adventure.text.Component.text(" 杀死了"))
                .build();
        } else {
            // 空手PvP
            return net.kyori.adventure.text.Component.text()
                .append(net.kyori.adventure.text.Component.text(victimName))
                .append(net.kyori.adventure.text.Component.text(" 被 "))
                .append(net.kyori.adventure.text.Component.text(killerName))
                .append(net.kyori.adventure.text.Component.text(" 杀死了"))
                .build();
        }
    }

    /**
     * 构建PvP远程死亡消息（弓箭等）
     */
    private net.kyori.adventure.text.Component buildPvPRangedDeathMessage(Player victim, Player shooter) {
        ItemStack weapon = shooter.getInventory().getItemInMainHand();

        // 获取纯玩家名（不包含team前缀等）
        String victimName = getPlainPlayerName(victim);
        String shooterName = getPlainPlayerName(shooter);

        if (weapon != null && (weapon.getType() == Material.BOW || weapon.getType() == Material.CROSSBOW)) {
            net.kyori.adventure.text.Component weaponComponent;

            if (weapon.hasItemMeta() && weapon.getItemMeta().hasDisplayName()) {
                weaponComponent = weapon.getItemMeta().displayName();
            } else {
                weaponComponent = net.kyori.adventure.text.Component.text(weapon.getType().name());
            }

            return net.kyori.adventure.text.Component.text()
                .append(net.kyori.adventure.text.Component.text(victimName))
                .append(net.kyori.adventure.text.Component.text(" 被 "))
                .append(net.kyori.adventure.text.Component.text(shooterName))
                .append(net.kyori.adventure.text.Component.text(" 用 "))
                .append(weaponComponent)
                .append(net.kyori.adventure.text.Component.text(" 射杀"))
                .build();
        } else {
            return net.kyori.adventure.text.Component.text()
                .append(net.kyori.adventure.text.Component.text(victimName))
                .append(net.kyori.adventure.text.Component.text(" 被 "))
                .append(net.kyori.adventure.text.Component.text(shooterName))
                .append(net.kyori.adventure.text.Component.text(" 射杀"))
                .build();
        }
    }

    /**
     * 获取纯玩家名（不包含team前缀、颜色代码等）
     */
    private String getPlainPlayerName(Player player) {
        // 使用玩家的基础名称，不包含任何前缀或后缀
        return player.getName();
    }

    /**
     * 发送完整的死亡消息到Velocity代理
     */
    private void sendCompleteDeathMessageToProxy(String playerName, String messageJson) {
        boolean debugEnabled = configManager.getBoolean("debug.enabled", false);

        try {
            // 检查插件消息通道是否已注册
            if (!plugin.getServer().getMessenger().isOutgoingChannelRegistered(plugin, DEATH_MESSAGE_CHANNEL)) {
                plugin.getLogger().warning("插件消息通道未注册: " + DEATH_MESSAGE_CHANNEL);
                return;
            }

            // 获取服务器名称
            String serverName = plugin.getServer().getName();
            if (serverName == null || serverName.isEmpty()) {
                serverName = "unknown";
            }

            // 构建消息数据 (格式: playerName|messageJson|serverName)
            String messageData = playerName + "|" + messageJson + "|" + serverName;
            byte[] data = messageData.getBytes(StandardCharsets.UTF_8);

            if (debugEnabled) {
                plugin.getLogger().info("准备发送完整死亡消息到代理:");
                plugin.getLogger().info("  玩家: " + playerName);
                plugin.getLogger().info("  服务器: " + serverName);
                plugin.getLogger().info("  消息长度: " + messageJson.length() + " 字符");
            }

            // 发送插件消息到代理
            plugin.getServer().sendPluginMessage(plugin, DEATH_MESSAGE_CHANNEL, data);

            if (debugEnabled) {
                plugin.getLogger().info("完整死亡消息已成功发送到代理");
            }

        } catch (Exception e) {
            plugin.getLogger().severe("发送完整死亡消息到代理时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 发送死亡消息到Velocity代理（旧方法，保留兼容性）
     */
    private void sendDeathMessageToProxy(String playerName, String deathCause, String killerName, String weapon) {
        boolean debugEnabled = configManager.getBoolean("debug.enabled", false);

        try {
            // 检查插件消息通道是否已注册
            if (!plugin.getServer().getMessenger().isOutgoingChannelRegistered(plugin, DEATH_MESSAGE_CHANNEL)) {
                plugin.getLogger().warning("插件消息通道未注册: " + DEATH_MESSAGE_CHANNEL);
                return;
            }

            // 获取服务器名称
            String serverName = plugin.getServer().getName();
            if (serverName == null || serverName.isEmpty()) {
                serverName = "unknown";
            }

            // 构建消息数据 (格式: playerName|deathCause|killerName|weapon|serverName)
            String messageData = playerName + "|" + deathCause + "|" +
                               (killerName != null ? killerName : "") + "|" +
                               (weapon != null ? weapon : "") + "|" + serverName;
            byte[] data = messageData.getBytes(StandardCharsets.UTF_8);

            if (debugEnabled) {
                plugin.getLogger().info("准备发送死亡消息到代理:");
                plugin.getLogger().info("  玩家: " + playerName);
                plugin.getLogger().info("  死亡原因: " + deathCause);
                plugin.getLogger().info("  杀手: " + (killerName != null ? killerName : "无"));
                plugin.getLogger().info("  武器: " + (weapon != null ? weapon : "无"));
                plugin.getLogger().info("  消息数据: " + messageData);
                plugin.getLogger().info("  通道: " + DEATH_MESSAGE_CHANNEL);
            }

            // 发送插件消息到代理
            plugin.getServer().sendPluginMessage(plugin, DEATH_MESSAGE_CHANNEL, data);

            if (debugEnabled) {
                plugin.getLogger().info("死亡消息已成功发送到代理");
            }

        } catch (Exception e) {
            plugin.getLogger().severe("发送死亡消息到代理时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 检查死亡消息功能是否启用
     */
    public boolean isEnabled() {
        return configManager.getBoolean("death_messages.enabled", true);
    }

    /**
     * 检查是否应该取消原始死亡消息
     */
    public boolean shouldCancelOriginalMessage() {
        return configManager.getBoolean("death_messages.cancel_original", true);
    }

    /**
     * 检查是否启用调试模式
     */
    public boolean isDebugEnabled() {
        return configManager.getBoolean("debug.enabled", false);
    }

    /**
     * 获取死亡消息通道
     */
    public static String getDeathMessageChannel() {
        return DEATH_MESSAGE_CHANNEL;
    }
}
