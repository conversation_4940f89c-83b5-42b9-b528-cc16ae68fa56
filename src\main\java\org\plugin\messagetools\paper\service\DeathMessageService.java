package org.plugin.messagetools.paper.service;

import org.bukkit.Material;
import org.bukkit.entity.Arrow;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.java.JavaPlugin;
import org.plugin.messagetools.paper.config.ConfigManager;
import org.plugin.messagetools.paper.util.DeathCauseAnalyzer;

import java.nio.charset.StandardCharsets;

/**
 * 死亡消息服务
 * 负责处理玩家死亡事件并发送到Velocity代理
 */
public class DeathMessageService {

    private final JavaPlugin plugin;
    private final ConfigManager configManager;
    private final DeathCauseAnalyzer deathCauseAnalyzer;

    // 插件消息通道
    private static final String DEATH_MESSAGE_CHANNEL = "messagetools:death";

    // 防重复发送机制
    private final java.util.Set<String> processedDeaths = java.util.concurrent.ConcurrentHashMap.newKeySet();
    
    public DeathMessageService(JavaPlugin plugin, ConfigManager configManager) {
        this.plugin = plugin;
        this.configManager = configManager;
        this.deathCauseAnalyzer = new DeathCauseAnalyzer();
    }
    
    /**
     * 处理玩家死亡事件
     */
    public void handlePlayerDeath(PlayerDeathEvent event) {
        Player player = event.getEntity();
        
        boolean debugEnabled = configManager.getBoolean("debug.enabled", false);
        boolean verboseEvents = configManager.getBoolean("debug.verbose_events", false);
        
        // 生成唯一的死亡事件ID（玩家名+时间戳）
        String deathId = player.getName() + "_" + System.currentTimeMillis();

        // 防重复处理机制
        if (!processedDeaths.add(deathId)) {
            if (debugEnabled) {
                plugin.getLogger().info("跳过重复的死亡事件: " + player.getName());
            }
            return;
        }

        // 清理旧的死亡记录（保留最近1000个）
        if (processedDeaths.size() > 1000) {
            processedDeaths.clear();
        }

        if (debugEnabled) {
            plugin.getLogger().info("处理玩家死亡事件: " + player.getName() + " (ID: " + deathId + ")");
        }

        // 检查死亡消息功能是否启用
        if (!configManager.getBoolean("death_messages.enabled", true)) {
            if (debugEnabled && verboseEvents) {
                plugin.getLogger().info("死亡消息功能已禁用，跳过处理");
            }
            return;
        }
        
        // 构建完整的死亡消息（使用原生API，包含悬停效果）
        net.kyori.adventure.text.Component deathMessage = buildCompleteDeathMessage(event);

        if (deathMessage != null) {
            // 将Component序列化为JSON
            String messageJson = net.kyori.adventure.text.serializer.gson.GsonComponentSerializer.gson().serialize(deathMessage);

            if (debugEnabled && verboseEvents) {
                plugin.getLogger().info("构建的死亡消息JSON: " + messageJson);
            }

            // 发送完整的死亡消息到Velocity代理
            sendCompleteDeathMessageToProxy(player.getName(), messageJson);
        } else {
            if (debugEnabled) {
                plugin.getLogger().warning("无法构建死亡消息: " + player.getName());
            }
        }
    }

    /**
     * 构建完整的死亡消息（使用纯玩家名，保持武器悬停效果）
     */
    private net.kyori.adventure.text.Component buildCompleteDeathMessage(PlayerDeathEvent event) {
        // 获取原始死亡消息
        net.kyori.adventure.text.Component originalMessage = event.deathMessage();

        if (originalMessage != null) {
            // 处理原始死亡消息，替换玩家名为纯玩家名
            return processOriginalDeathMessage(originalMessage, event);
        }

        // 如果没有原始死亡消息，使用自定义构建
        return buildCustomDeathMessage(event);
    }

    /**
     * 处理原始死亡消息，替换玩家名为纯玩家名
     */
    private net.kyori.adventure.text.Component processOriginalDeathMessage(net.kyori.adventure.text.Component originalMessage, PlayerDeathEvent event) {
        Player player = event.getEntity();
        String originalPlayerName = player.getName();
        String plainPlayerName = getPlainPlayerName(player);

        // 如果玩家名相同，直接返回原始消息
        if (originalPlayerName.equals(plainPlayerName)) {
            return originalMessage;
        }

        // 递归处理Component，替换玩家名
        return replacePlayerNameInComponent(originalMessage, originalPlayerName, plainPlayerName, event);
    }

    /**
     * 递归替换Component中的玩家名
     */
    private net.kyori.adventure.text.Component replacePlayerNameInComponent(
            net.kyori.adventure.text.Component component,
            String originalName,
            String plainName,
            PlayerDeathEvent event) {

        if (component == null) {
            return null;
        }

        // 处理文本组件
        if (component instanceof net.kyori.adventure.text.TextComponent) {
            net.kyori.adventure.text.TextComponent textComponent = (net.kyori.adventure.text.TextComponent) component;
            String content = textComponent.content();

            // 替换文本中的玩家名
            if (content.contains(originalName)) {
                content = content.replace(originalName, plainName);
            }

            // 创建新的文本组件
            net.kyori.adventure.text.TextComponent.Builder builder = net.kyori.adventure.text.Component.text()
                .content(content)
                .style(textComponent.style());

            // 递归处理子组件
            for (net.kyori.adventure.text.Component child : textComponent.children()) {
                net.kyori.adventure.text.Component processedChild = replacePlayerNameInComponent(child, originalName, plainName, event);
                if (processedChild != null) {
                    builder.append(processedChild);
                }
            }

            return builder.build();
        }

        // 对于其他类型的组件，如果包含玩家名，则回退到自定义构建
        String componentText = net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer.plainText().serialize(component);
        if (componentText.contains(originalName)) {
            // 如果组件包含原始玩家名，回退到自定义构建
            return buildCustomDeathMessage(event);
        }

        // 否则保持原组件不变
        return component;
    }

    /**
     * 手动构建自定义死亡消息（使用纯玩家名）
     */
    private net.kyori.adventure.text.Component buildCustomDeathMessage(PlayerDeathEvent event) {
        Player player = event.getEntity();
        EntityDamageEvent damageEvent = player.getLastDamageCause();

        String playerName = getPlainPlayerName(player);

        if (damageEvent == null) {
            return net.kyori.adventure.text.Component.text(playerName + " 死了");
        }

        // 根据伤害类型构建消息
        if (damageEvent instanceof org.bukkit.event.entity.EntityDamageByEntityEvent) {
            org.bukkit.event.entity.EntityDamageByEntityEvent entityEvent =
                (org.bukkit.event.entity.EntityDamageByEntityEvent) damageEvent;
            Entity damager = entityEvent.getDamager();

            if (damager instanceof Player) {
                return buildPvPDeathMessage(player, (Player) damager);
            } else if (damager instanceof Arrow) {
                Arrow arrow = (Arrow) damager;
                if (arrow.getShooter() instanceof Player) {
                    return buildPvPRangedDeathMessage(player, (Player) arrow.getShooter());
                }
                // 骷髅等怪物射箭
                return net.kyori.adventure.text.Component.text(playerName + " 被射死了");
            } else if (damager instanceof org.bukkit.entity.Zombie) {
                return net.kyori.adventure.text.Component.text(playerName + " 被僵尸杀死了");
            } else if (damager instanceof org.bukkit.entity.Skeleton) {
                return net.kyori.adventure.text.Component.text(playerName + " 被骷髅杀死了");
            } else if (damager instanceof org.bukkit.entity.Creeper) {
                return net.kyori.adventure.text.Component.text(playerName + " 被苦力怕炸死了");
            } else {
                return net.kyori.adventure.text.Component.text(playerName + " 被怪物杀死了");
            }
        }

        // 根据伤害原因构建环境死亡消息
        switch (damageEvent.getCause()) {
            case FALL:
                return net.kyori.adventure.text.Component.text(playerName + " 落地过猛");
            case DROWNING:
                return net.kyori.adventure.text.Component.text(playerName + " 淹死了");
            case LAVA:
                return net.kyori.adventure.text.Component.text(playerName + " 试图在熔岩里游泳");
            case FIRE:
            case FIRE_TICK:
                return net.kyori.adventure.text.Component.text(playerName + " 被烧死了");
            case SUFFOCATION:
                return net.kyori.adventure.text.Component.text(playerName + " 在墙里窒息而亡");
            case VOID:
                return net.kyori.adventure.text.Component.text(playerName + " 掉出了这个世界");
            case STARVATION:
                return net.kyori.adventure.text.Component.text(playerName + " 饿死了");
            case POISON:
                return net.kyori.adventure.text.Component.text(playerName + " 中毒身亡");
            case WITHER:
                return net.kyori.adventure.text.Component.text(playerName + " 凋零了");
            case LIGHTNING:
                return net.kyori.adventure.text.Component.text(playerName + " 被闪电击中");
            case ENTITY_EXPLOSION:
            case BLOCK_EXPLOSION:
                return net.kyori.adventure.text.Component.text(playerName + " 爆炸了");
            default:
                return net.kyori.adventure.text.Component.text(playerName + " 死了");
        }
    }

    /**
     * 构建PvP死亡消息（近战）
     */
    private net.kyori.adventure.text.Component buildPvPDeathMessage(Player victim, Player killer) {
        ItemStack weapon = killer.getInventory().getItemInMainHand();

        // 获取纯玩家名（不包含team前缀等）
        String victimName = getPlainPlayerName(victim);
        String killerName = getPlainPlayerName(killer);

        if (weapon != null && weapon.getType() != Material.AIR) {
            // 有武器的PvP
            net.kyori.adventure.text.Component weaponComponent;

            if (weapon.hasItemMeta() && weapon.getItemMeta().hasDisplayName()) {
                // 使用武器的显示名称（保持原生悬停效果）
                weaponComponent = weapon.getItemMeta().displayName();
            } else {
                // 使用材料名称
                weaponComponent = net.kyori.adventure.text.Component.text(weapon.getType().name());
            }

            return net.kyori.adventure.text.Component.text()
                .append(net.kyori.adventure.text.Component.text(victimName))
                .append(net.kyori.adventure.text.Component.text(" 被 "))
                .append(net.kyori.adventure.text.Component.text(killerName))
                .append(net.kyori.adventure.text.Component.text(" 用 "))
                .append(weaponComponent)
                .append(net.kyori.adventure.text.Component.text(" 杀死了"))
                .build();
        } else {
            // 空手PvP
            return net.kyori.adventure.text.Component.text()
                .append(net.kyori.adventure.text.Component.text(victimName))
                .append(net.kyori.adventure.text.Component.text(" 被 "))
                .append(net.kyori.adventure.text.Component.text(killerName))
                .append(net.kyori.adventure.text.Component.text(" 杀死了"))
                .build();
        }
    }

    /**
     * 构建PvP远程死亡消息（弓箭等）
     */
    private net.kyori.adventure.text.Component buildPvPRangedDeathMessage(Player victim, Player shooter) {
        ItemStack weapon = shooter.getInventory().getItemInMainHand();

        // 获取纯玩家名（不包含team前缀等）
        String victimName = getPlainPlayerName(victim);
        String shooterName = getPlainPlayerName(shooter);

        if (weapon != null && (weapon.getType() == Material.BOW || weapon.getType() == Material.CROSSBOW)) {
            net.kyori.adventure.text.Component weaponComponent;

            if (weapon.hasItemMeta() && weapon.getItemMeta().hasDisplayName()) {
                weaponComponent = weapon.getItemMeta().displayName();
            } else {
                weaponComponent = net.kyori.adventure.text.Component.text(weapon.getType().name());
            }

            return net.kyori.adventure.text.Component.text()
                .append(net.kyori.adventure.text.Component.text(victimName))
                .append(net.kyori.adventure.text.Component.text(" 被 "))
                .append(net.kyori.adventure.text.Component.text(shooterName))
                .append(net.kyori.adventure.text.Component.text(" 用 "))
                .append(weaponComponent)
                .append(net.kyori.adventure.text.Component.text(" 射杀"))
                .build();
        } else {
            return net.kyori.adventure.text.Component.text()
                .append(net.kyori.adventure.text.Component.text(victimName))
                .append(net.kyori.adventure.text.Component.text(" 被 "))
                .append(net.kyori.adventure.text.Component.text(shooterName))
                .append(net.kyori.adventure.text.Component.text(" 射杀"))
                .build();
        }
    }

    /**
     * 获取纯玩家名（不包含team前缀、颜色代码等）
     */
    private String getPlainPlayerName(Player player) {
        // 使用玩家的基础名称，不包含任何前缀或后缀
        return player.getName();
    }

    /**
     * 发送完整的死亡消息到Velocity代理
     */
    private void sendCompleteDeathMessageToProxy(String playerName, String messageJson) {
        boolean debugEnabled = configManager.getBoolean("debug.enabled", false);

        try {
            // 检查插件消息通道是否已注册
            if (!plugin.getServer().getMessenger().isOutgoingChannelRegistered(plugin, DEATH_MESSAGE_CHANNEL)) {
                plugin.getLogger().warning("插件消息通道未注册: " + DEATH_MESSAGE_CHANNEL);
                return;
            }

            // 获取服务器名称
            String serverName = plugin.getServer().getName();
            if (serverName == null || serverName.isEmpty()) {
                serverName = "unknown";
            }

            // 构建消息数据 (格式: playerName|messageJson|serverName)
            String messageData = playerName + "|" + messageJson + "|" + serverName;
            byte[] data = messageData.getBytes(StandardCharsets.UTF_8);

            if (debugEnabled) {
                plugin.getLogger().info("准备发送完整死亡消息到代理:");
                plugin.getLogger().info("  玩家: " + playerName);
                plugin.getLogger().info("  服务器: " + serverName);
                plugin.getLogger().info("  消息长度: " + messageJson.length() + " 字符");
            }

            // 发送插件消息到代理
            plugin.getServer().sendPluginMessage(plugin, DEATH_MESSAGE_CHANNEL, data);

            if (debugEnabled) {
                plugin.getLogger().info("完整死亡消息已成功发送到代理");
            }

        } catch (Exception e) {
            plugin.getLogger().severe("发送完整死亡消息到代理时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 发送死亡消息到Velocity代理（旧方法，保留兼容性）
     */
    private void sendDeathMessageToProxy(String playerName, String deathCause, String killerName, String weapon) {
        boolean debugEnabled = configManager.getBoolean("debug.enabled", false);

        try {
            // 检查插件消息通道是否已注册
            if (!plugin.getServer().getMessenger().isOutgoingChannelRegistered(plugin, DEATH_MESSAGE_CHANNEL)) {
                plugin.getLogger().warning("插件消息通道未注册: " + DEATH_MESSAGE_CHANNEL);
                return;
            }

            // 获取服务器名称
            String serverName = plugin.getServer().getName();
            if (serverName == null || serverName.isEmpty()) {
                serverName = "unknown";
            }

            // 构建消息数据 (格式: playerName|deathCause|killerName|weapon|serverName)
            String messageData = playerName + "|" + deathCause + "|" +
                               (killerName != null ? killerName : "") + "|" +
                               (weapon != null ? weapon : "") + "|" + serverName;
            byte[] data = messageData.getBytes(StandardCharsets.UTF_8);

            if (debugEnabled) {
                plugin.getLogger().info("准备发送死亡消息到代理:");
                plugin.getLogger().info("  玩家: " + playerName);
                plugin.getLogger().info("  死亡原因: " + deathCause);
                plugin.getLogger().info("  杀手: " + (killerName != null ? killerName : "无"));
                plugin.getLogger().info("  武器: " + (weapon != null ? weapon : "无"));
                plugin.getLogger().info("  消息数据: " + messageData);
                plugin.getLogger().info("  通道: " + DEATH_MESSAGE_CHANNEL);
            }

            // 发送插件消息到代理
            plugin.getServer().sendPluginMessage(plugin, DEATH_MESSAGE_CHANNEL, data);

            if (debugEnabled) {
                plugin.getLogger().info("死亡消息已成功发送到代理");
            }

        } catch (Exception e) {
            plugin.getLogger().severe("发送死亡消息到代理时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 检查死亡消息功能是否启用
     */
    public boolean isEnabled() {
        return configManager.getBoolean("death_messages.enabled", true);
    }

    /**
     * 检查是否应该取消原始死亡消息
     */
    public boolean shouldCancelOriginalMessage() {
        return configManager.getBoolean("death_messages.cancel_original", true);
    }

    /**
     * 检查是否启用调试模式
     */
    public boolean isDebugEnabled() {
        return configManager.getBoolean("debug.enabled", false);
    }

    /**
     * 获取死亡消息通道
     */
    public static String getDeathMessageChannel() {
        return DEATH_MESSAGE_CHANNEL;
    }
}
