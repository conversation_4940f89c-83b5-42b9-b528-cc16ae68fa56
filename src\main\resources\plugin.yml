name: MessageTools-Paper
version: '1.0-SNAPSHOT'
main: org.plugin.messagetools.paper.MessageToolsPaper
api-version: '1.20'
authors: [NSrank, Augment]
description: MessageTools Paper端插件，处理死亡消息并发送到Velocity代理
website: https://github.com/NSrank/MessageTools

# 软依赖（可选依赖）
softdepend:
  - PlaceholderAPI
  - Vault
  - LuckPerms

# 权限定义
permissions:
  messagetools.admin:
    description: MessageTools管理员权限
    default: op
    children:
      messagetools.reload: true
      messagetools.debug: true
  
  messagetools.reload:
    description: 重载配置文件权限
    default: op
  
  messagetools.debug:
    description: 查看调试信息权限
    default: op
  
  messagetools.bypass:
    description: 绕过死亡消息处理权限
    default: false

# 命令定义
commands:
  messagetools-paper:
    description: MessageTools-Paper主命令
    usage: /<command> [reload|debug|status]
    permission: messagetools.admin
    aliases: [mtp, mtpaper]

# 加载时机
load: STARTUP

# 前缀
prefix: MessageTools-Paper
