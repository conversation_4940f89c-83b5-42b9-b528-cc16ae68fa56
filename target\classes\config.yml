# MessageTools-Paper 配置文件
# Paper端插件配置，负责处理死亡消息并发送到Velocity代理

# 死亡消息配置
death_messages:
  # 是否启用死亡消息功能
  enabled: true
  
  # 是否取消原始的死亡消息（避免重复显示）
  cancel_original: true
  
  # 死亡原因映射配置
  # 这里配置的是发送给Velocity的死亡原因标识符
  # Velocity端会根据这些标识符显示对应的中文消息
  cause_mapping:
    # 自然死亡
    fall: "fall"              # 摔死
    drown: "drown"            # 溺水
    lava: "lava"              # 岩浆
    fire: "fire"              # 火焰
    suffocation: "suffocation" # 窒息
    void: "void"              # 虚空
    starve: "starve"          # 饥饿
    poison: "poison"          # 中毒
    magic: "magic"            # 魔法
    wither: "wither"          # 凋零
    
    # 爆炸
    explosion: "explosion"    # 爆炸
    tnt: "tnt"               # TNT
    
    # 生物攻击
    mob: "mob"               # 一般怪物
    zombie: "zombie"         # 僵尸
    skeleton: "skeleton"     # 骷髅
    spider: "spider"         # 蜘蛛
    creeper: "creeper"       # 苦力怕
    enderman: "enderman"     # 末影人
    
    # 玩家PvP
    player: "player"         # 玩家近战攻击
    player_bow: "player_bow" # 玩家弓箭攻击
    
    # 投射物
    projectile: "projectile" # 其他投射物
    
    # 未知原因
    unknown: "unknown"       # 未知死亡原因

# 武器名称翻译配置
weapon_translations:
  # 剑类
  wooden_sword: "木剑"
  stone_sword: "石剑"
  iron_sword: "铁剑"
  golden_sword: "金剑"
  diamond_sword: "钻石剑"
  netherite_sword: "下界合金剑"
  
  # 斧类
  wooden_axe: "木斧"
  stone_axe: "石斧"
  iron_axe: "铁斧"
  golden_axe: "金斧"
  diamond_axe: "钻石斧"
  netherite_axe: "下界合金斧"
  
  # 远程武器
  bow: "弓"
  crossbow: "弩"
  trident: "三叉戟"
  
  # 工具类
  wooden_pickaxe: "木镐"
  stone_pickaxe: "石镐"
  iron_pickaxe: "铁镐"
  golden_pickaxe: "金镐"
  diamond_pickaxe: "钻石镐"
  netherite_pickaxe: "下界合金镐"
  
  wooden_shovel: "木铲"
  stone_shovel: "石铲"
  iron_shovel: "铁铲"
  golden_shovel: "金铲"
  diamond_shovel: "钻石铲"
  netherite_shovel: "下界合金铲"
  
  wooden_hoe: "木锄"
  stone_hoe: "石锄"
  iron_hoe: "铁锄"
  golden_hoe: "金锄"
  diamond_hoe: "钻石锄"
  netherite_hoe: "下界合金锄"

# 调试配置
debug:
  # 是否启用调试模式
  enabled: false
  
  # 是否输出详细的事件处理日志
  verbose_events: false
  
  # 是否输出死亡原因分析详情
  verbose_death_analysis: false

# 插件消息通道配置
plugin_messaging:
  # 死亡消息通道标识符
  death_channel: "messagetools:death"
  
  # 消息发送超时时间（毫秒）
  timeout: 5000
  
  # 是否在发送失败时重试
  retry_on_failure: true
  
  # 最大重试次数
  max_retries: 3

# 性能配置
performance:
  # 是否异步处理死亡事件
  async_processing: false
  
  # 死亡消息处理队列大小
  queue_size: 100
  
  # 是否启用死亡消息缓存
  enable_cache: false
  
  # 缓存过期时间（秒）
  cache_expire_time: 60

# 兼容性配置
compatibility:
  # 是否与其他死亡消息插件兼容
  compatible_with_other_plugins: true
  
  # 兼容的插件列表
  compatible_plugins:
    - "DeathMessages"
    - "CustomDeathMessages"
    - "AdvancedDeathMessages"
  
  # 是否在检测到冲突插件时禁用功能
  disable_on_conflict: false
