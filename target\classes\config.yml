# MessageTools 配置文件 (Velocity端)
# 支持原版颜色代码 (&a, &b, &c等) 和格式代码 (&l, &n, &o等)
# 支持PlaceholderAPI变量 (需要PAPIProxyBridge)

# 消息配置
messages:
  # 玩家加入群组服时在全服聊天中发送的消息
  join:
    enabled: true
    tick: 20  # 玩家加入后20tick(1秒)后发送
    messages:
      - "&a[+] &f%player_name% &7加入了服务器"
      - "&7欢迎 &a%player_name% &7来到我们的服务器！"

  # 玩家退出群组服时在全服聊天中发送的消息
  quit:
    enabled: true
    tick: 0  # 立即发送
    messages:
      - "&c[-] &f%player_name% &7离开了服务器"

  # 玩家加入时向玩家发送的私人消息
  message:
    enabled: true
    tick: 60  # 玩家加入后60tick(3秒)后发送
    messages:
      - "&a欢迎来到服务器，%player_name%！"
      - "&7当前在线玩家数：&a%server_online%"
      - "&7服务器时间：&e%server_time_HH:mm:ss%"

  # 玩家第一次加入时在全服聊天中发送的消息
  first_join:
    enabled: true
    tick: 10  # 玩家加入后10tick(0.5秒)后发送，优先于普通加入消息
    messages:
      - "&6★ &f%player_name% &e第一次加入了服务器！"
      - "&e让我们热烈欢迎新玩家！"

  # 玩家第一次退出时在全服聊天中发送的消息
  first_quit:
    enabled: true
    tick: 0  # 立即发送
    messages:
      - "&6★ &f%player_name% &e第一次离开了服务器"

# 死亡消息配置
death_messages:
  # 是否启用死亡消息功能
  enabled: true
  # 是否广播到所有服务器
  broadcast_to_all_servers: true
  # 是否包含来源服务器的玩家（设为false避免重复发送）
  include_source_server: false
  # 是否在控制台输出死亡消息
  console_output: true

# 调试配置
debug:
  # 是否启用调试模式
  enabled: true
  # 是否输出详细的事件处理日志
  verbose_events: true
  # 是否输出配置加载日志
  verbose_config: false


# 性能配置
performance:
  # 是否异步处理死亡事件
  async_processing: false
  
  # 死亡消息处理队列大小
  queue_size: 100
  
  # 是否启用死亡消息缓存
  enable_cache: false
  
  # 缓存过期时间（秒）
  cache_expire_time: 60

# 兼容性配置
compatibility:
  # 是否与其他死亡消息插件兼容
  compatible_with_other_plugins: true
  
  # 兼容的插件列表
  compatible_plugins:
    - "DeathMessages"
    - "CustomDeathMessages"
    - "AdvancedDeathMessages"
  
  # 是否在检测到冲突插件时禁用功能
  disable_on_conflict: false
