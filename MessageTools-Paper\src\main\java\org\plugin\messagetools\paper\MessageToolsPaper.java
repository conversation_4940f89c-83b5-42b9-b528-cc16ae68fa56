package org.plugin.messagetools.paper;

import org.bukkit.plugin.java.JavaPlugin;
import org.plugin.messagetools.paper.config.ConfigManager;
import org.plugin.messagetools.paper.listener.DeathEventListener;
import org.plugin.messagetools.paper.service.DeathMessageService;

/**
 * MessageTools Paper端主类
 * 负责监听死亡事件并发送到Velocity代理
 */
public class MessageToolsPaper extends JavaPlugin {
    
    private ConfigManager configManager;
    private DeathMessageService deathMessageService;
    private DeathEventListener deathEventListener;
    
    @Override
    public void onEnable() {
        getLogger().info("MessageTools-Paper 插件正在启动...");
        
        try {
            // 初始化配置管理器
            configManager = new ConfigManager(this);
            configManager.loadConfig();
            
            // 初始化死亡消息服务
            deathMessageService = new DeathMessageService(this, configManager);
            
            // 初始化死亡事件监听器
            deathEventListener = new DeathEventListener(deathMessageService, getLogger());
            
            // 注册事件监听器
            getServer().getPluginManager().registerEvents(deathEventListener, this);
            
            // 注册插件消息通道
            getServer().getMessenger().registerOutgoingPluginChannel(this, "messagetools:death");
            
            getLogger().info("===================================");
            getLogger().info("MessageTools-Paper v1.0 已加载");
            getLogger().info("作者：NSrank & Augment");
            getLogger().info("===================================");
            
            // 调试信息
            boolean debugEnabled = configManager.getBoolean("debug.enabled", false);
            if (debugEnabled) {
                getLogger().info("调试模式已启用");
                getLogger().info("死亡消息功能: {}", configManager.getBoolean("death_messages.enabled", true) ? "启用" : "禁用");
                getLogger().info("插件消息通道: messagetools:death");
            }
            
        } catch (Exception e) {
            getLogger().severe("MessageTools-Paper 插件启动失败: " + e.getMessage());
            e.printStackTrace();
            getServer().getPluginManager().disablePlugin(this);
        }
    }
    
    @Override
    public void onDisable() {
        getLogger().info("MessageTools-Paper 插件正在关闭...");
        
        try {
            // 保存配置文件
            if (configManager != null) {
                configManager.saveConfig();
            }
            
            // 注销插件消息通道
            getServer().getMessenger().unregisterOutgoingPluginChannel(this);
            
            getLogger().info("MessageTools-Paper 插件关闭完成");
            
        } catch (Exception e) {
            getLogger().severe("MessageTools-Paper 插件关闭时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 获取配置管理器
     */
    public ConfigManager getConfigManager() {
        return configManager;
    }
    
    /**
     * 获取死亡消息服务
     */
    public DeathMessageService getDeathMessageService() {
        return deathMessageService;
    }
}
