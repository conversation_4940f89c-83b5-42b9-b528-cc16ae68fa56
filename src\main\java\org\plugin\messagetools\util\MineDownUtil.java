package org.plugin.messagetools.util;

import de.themoep.minedown.adventure.MineDown;
import net.kyori.adventure.text.Component;

/**
 * MineDown工具类
 * 提供MineDown格式的文本处理功能
 */
public class MineDownUtil {

    /**
     * 将MineDown格式的文本转换为Adventure Component
     * 
     * @param text MineDown格式的文本
     * @return Adventure Component
     */
    public static Component parse(String text) {
        if (text == null || text.isEmpty()) {
            return Component.empty();
        }
        
        try {
            return new MineDown(text).toComponent();
        } catch (Exception e) {
            // 如果解析失败，返回纯文本
            return Component.text(text);
        }
    }
    
    /**
     * 将MineDown格式的文本转换为Adventure Component，支持占位符替换
     * 
     * @param text MineDown格式的文本
     * @param replacements 占位符替换映射
     * @return Adventure Component
     */
    public static Component parse(String text, java.util.Map<String, Object> replacements) {
        if (text == null || text.isEmpty()) {
            return Component.empty();
        }
        
        try {
            MineDown mineDown = new MineDown(text);
            
            // 应用占位符替换
            if (replacements != null && !replacements.isEmpty()) {
                for (java.util.Map.Entry<String, Object> entry : replacements.entrySet()) {
                    String placeholder = entry.getKey();
                    Object value = entry.getValue();
                    
                    if (value instanceof Component) {
                        mineDown.replace(placeholder, (Component) value);
                    } else {
                        mineDown.replace(placeholder, String.valueOf(value));
                    }
                }
            }
            
            return mineDown.toComponent();
        } catch (Exception e) {
            // 如果解析失败，返回纯文本
            return Component.text(text);
        }
    }
    
    /**
     * 检查文本是否包含MineDown格式代码
     * 
     * @param text 要检查的文本
     * @return 如果包含MineDown格式代码则返回true
     */
    public static boolean containsMineDownFormat(String text) {
        if (text == null || text.isEmpty()) {
            return false;
        }
        
        // 检查常见的MineDown格式
        return text.contains("&") ||           // 颜色代码
               text.contains("**") ||          // 粗体
               text.contains("##") ||          // 斜体
               text.contains("__") ||          // 下划线
               text.contains("~~") ||          // 删除线
               text.contains("??") ||          // 混淆
               text.contains("[") ||           // 链接/事件
               text.contains("&#");            // RGB颜色
    }
    
    /**
     * 为消息应用MineDown格式
     * 
     * @param message 原始消息
     * @return 格式化的消息Component
     */
    public static Component formatMessage(String message) {
        if (message == null || message.isEmpty()) {
            return Component.empty();
        }
        
        // 如果包含MineDown格式，则解析
        if (containsMineDownFormat(message)) {
            return parse(message);
        }
        
        // 否则返回纯文本
        return Component.text(message);
    }
    
    /**
     * 为死亡消息应用彩虹效果
     * 
     * @param playerName 玩家名
     * @param message 死亡消息
     * @return 彩虹效果的死亡消息
     */
    public static Component formatRainbowDeathMessage(String playerName, String message) {
        String rainbowMessage = "&rainbow&" + playerName + " &7" + message;
        return parse(rainbowMessage);
    }
    
    /**
     * 为死亡消息应用渐变色效果
     * 
     * @param playerName 玩家名
     * @param message 死亡消息
     * @param startColor 起始颜色（十六进制，如 "ff0000"）
     * @param endColor 结束颜色（十六进制，如 "0000ff"）
     * @return 渐变色效果的死亡消息
     */
    public static Component formatGradientDeathMessage(String playerName, String message, String startColor, String endColor) {
        String gradientMessage = "&#" + startColor + "-#" + endColor + "&" + playerName + " &7" + message;
        return parse(gradientMessage);
    }
    
    /**
     * 为PvP死亡消息应用特殊格式
     * 
     * @param victimName 受害者名称
     * @param killerName 击杀者名称
     * @param weaponName 武器名称（可为null）
     * @return 格式化的PvP死亡消息
     */
    public static Component formatPvPDeathMessage(String victimName, String killerName, String weaponName) {
        StringBuilder message = new StringBuilder();
        
        if (weaponName != null && !weaponName.isEmpty()) {
            // 有武器的PvP
            message.append("&#ff6b6b-#ee5a52&").append(victimName)
                   .append(" &7被 &#4ecdc4-#44a08d&").append(killerName)
                   .append(" &7用 &#ffd93d-#6bcf7f&[").append(weaponName).append("] &7杀死了");
        } else {
            // 无武器的PvP
            message.append("&#ff6b6b-#ee5a52&").append(victimName)
                   .append(" &7被 &#4ecdc4-#44a08d&").append(killerName)
                   .append(" &7杀死了");
        }
        
        return parse(message.toString());
    }
    
    /**
     * 为环境死亡消息应用特殊格式
     * 
     * @param playerName 玩家名称
     * @param deathCause 死亡原因
     * @return 格式化的环境死亡消息
     */
    public static Component formatEnvironmentDeathMessage(String playerName, String deathCause) {
        StringBuilder message = new StringBuilder();
        
        switch (deathCause.toLowerCase()) {
            case "fall":
                message.append("&#ff9a9e-#fecfef&").append(playerName).append(" &7落地过猛");
                break;
            case "drown":
                message.append("&#a8edea-#fed6e3&").append(playerName).append(" &9淹死了");
                break;
            case "lava":
                message.append("&#ff9a56-#ff6b35&").append(playerName).append(" &6试图在熔岩里游泳");
                break;
            case "fire":
                message.append("&#ff9a56-#ff6b35&").append(playerName).append(" &6被烧死了");
                break;
            case "explosion":
                message.append("&#ffd93d-#6bcf7f&").append(playerName).append(" &e爆炸了");
                break;
            case "void":
                message.append("&#667eea-#764ba2&").append(playerName).append(" &5掉出了这个世界");
                break;
            default:
                message.append("&rainbow&").append(playerName).append(" &7死了");
                break;
        }
        
        return parse(message.toString());
    }
    
    /**
     * 为怪物攻击死亡消息应用特殊格式
     * 
     * @param playerName 玩家名称
     * @param mobType 怪物类型
     * @return 格式化的怪物攻击死亡消息
     */
    public static Component formatMobDeathMessage(String playerName, String mobType) {
        StringBuilder message = new StringBuilder();
        
        switch (mobType.toLowerCase()) {
            case "zombie":
                message.append("&#ff6b6b-#ee5a52&").append(playerName).append(" &2被僵尸杀死了");
                break;
            case "skeleton":
                message.append("&#ff6b6b-#ee5a52&").append(playerName).append(" &f被骷髅杀死了");
                break;
            case "creeper":
                message.append("&#ff6b6b-#ee5a52&").append(playerName).append(" &a被苦力怕炸死了");
                break;
            case "spider":
                message.append("&#ff6b6b-#ee5a52&").append(playerName).append(" &8被蜘蛛杀死了");
                break;
            case "enderman":
                message.append("&#ff6b6b-#ee5a52&").append(playerName).append(" &5被末影人杀死了");
                break;
            default:
                message.append("&#ff6b6b-#ee5a52&").append(playerName).append(" &7被怪物杀死了");
                break;
        }
        
        return parse(message.toString());
    }
    
    /**
     * 应用特殊效果到文本
     * 
     * @param text 原始文本
     * @param effect 效果类型（rainbow, gradient, bold, italic等）
     * @return 应用效果后的Component
     */
    public static Component applyEffect(String text, String effect) {
        if (text == null || text.isEmpty() || effect == null) {
            return Component.text(text != null ? text : "");
        }
        
        String formattedText;
        switch (effect.toLowerCase()) {
            case "rainbow":
                formattedText = "&rainbow&" + text;
                break;
            case "bold":
                formattedText = "**" + text + "**";
                break;
            case "italic":
                formattedText = "##" + text + "##";
                break;
            case "underline":
                formattedText = "__" + text + "__";
                break;
            case "strikethrough":
                formattedText = "~~" + text + "~~";
                break;
            case "obfuscated":
                formattedText = "??" + text + "??";
                break;
            case "red_gradient":
                formattedText = "&#ff0000-#800000&" + text;
                break;
            case "blue_gradient":
                formattedText = "&#0000ff-#000080&" + text;
                break;
            case "green_gradient":
                formattedText = "&#00ff00-#008000&" + text;
                break;
            default:
                formattedText = text;
                break;
        }
        
        return parse(formattedText);
    }
}
