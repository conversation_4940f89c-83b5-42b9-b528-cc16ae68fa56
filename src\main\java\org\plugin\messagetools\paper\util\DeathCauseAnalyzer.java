package org.plugin.messagetools.paper.util;

import org.bukkit.Material;
import org.bukkit.entity.*;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.inventory.ItemStack;

/**
 * 死亡原因分析器
 * 负责分析玩家死亡的具体原因
 */
public class DeathCauseAnalyzer {
    
    /**
     * 分析玩家死亡原因
     */
    public String analyzeDeath(PlayerDeathEvent event) {
        Player player = event.getEntity();
        EntityDamageEvent damageEvent = player.getLastDamageCause();
        
        if (damageEvent == null) {
            return "unknown";
        }
        
        EntityDamageEvent.DamageCause cause = damageEvent.getCause();
        
        // 根据伤害原因分析具体死亡类型
        switch (cause) {
            case FALL:
                return "fall";
            case DROWNING:
                return "drown";
            case LAVA:
                return "lava";
            case FIRE:
            case FIRE_TICK:
                return "fire";
            case SUFFOCATION:
                return "suffocation";
            case VOID:
                return "void";
            case STARVATION:
                return "starve";
            case POISON:
                return "poison";
            case MAGIC:
                return "magic";
            case WITHER:
                return "wither";
            case ENTITY_EXPLOSION:
                return "explosion";
            case BLOCK_EXPLOSION:
                return "tnt";
            case ENTITY_ATTACK:
                return analyzeEntityAttack(damageEvent);
            case PROJECTILE:
                return analyzeProjectileAttack(damageEvent);
            default:
                return "unknown";
        }
    }
    
    /**
     * 分析实体攻击类型
     */
    private String analyzeEntityAttack(EntityDamageEvent damageEvent) {
        if (!(damageEvent instanceof org.bukkit.event.entity.EntityDamageByEntityEvent)) {
            return "mob";
        }
        
        org.bukkit.event.entity.EntityDamageByEntityEvent entityEvent = 
            (org.bukkit.event.entity.EntityDamageByEntityEvent) damageEvent;
        Entity damager = entityEvent.getDamager();
        
        if (damager instanceof Player) {
            Player killer = (Player) damager;
            ItemStack weapon = killer.getInventory().getItemInMainHand();

            if (weapon != null && weapon.getType() != Material.AIR) {
                return "player_weapon"; // 玩家使用武器攻击
            } else {
                return "player"; // 玩家空手攻击
            }
        } else if (damager instanceof Zombie) {
            return "zombie";
        } else if (damager instanceof Skeleton) {
            return "skeleton";
        } else if (damager instanceof Spider) {
            return "spider";
        } else if (damager instanceof Creeper) {
            return "creeper";
        } else if (damager instanceof Enderman) {
            return "enderman";
        } else {
            return "mob";
        }
    }
    
    /**
     * 分析投射物攻击类型
     */
    private String analyzeProjectileAttack(EntityDamageEvent damageEvent) {
        if (!(damageEvent instanceof org.bukkit.event.entity.EntityDamageByEntityEvent)) {
            return "projectile";
        }
        
        org.bukkit.event.entity.EntityDamageByEntityEvent entityEvent = 
            (org.bukkit.event.entity.EntityDamageByEntityEvent) damageEvent;
        Entity projectile = entityEvent.getDamager();
        
        if (projectile instanceof Arrow) {
            Arrow arrow = (Arrow) projectile;
            if (arrow.getShooter() instanceof Player) {
                Player shooter = (Player) arrow.getShooter();
                ItemStack weapon = shooter.getInventory().getItemInMainHand();

                if (weapon != null && (weapon.getType() == Material.BOW || weapon.getType() == Material.CROSSBOW)) {
                    return "player_bow_weapon"; // 玩家使用弓/弩攻击
                } else {
                    return "player_bow"; // 玩家弓箭攻击（无武器信息）
                }
            } else if (arrow.getShooter() instanceof Skeleton) {
                return "skeleton";
            }
        }
        
        return "projectile";
    }
    
    /**
     * 获取杀手玩家名称
     */
    public String getKillerName(PlayerDeathEvent event) {
        Player player = event.getEntity();
        EntityDamageEvent damageEvent = player.getLastDamageCause();

        if (damageEvent == null) {
            return null;
        }

        if (!(damageEvent instanceof org.bukkit.event.entity.EntityDamageByEntityEvent)) {
            return null;
        }

        org.bukkit.event.entity.EntityDamageByEntityEvent entityEvent =
            (org.bukkit.event.entity.EntityDamageByEntityEvent) damageEvent;
        Entity damager = entityEvent.getDamager();

        // 玩家攻击
        if (damager instanceof Player) {
            return ((Player) damager).getName();
        }

        // 投射物攻击
        if (damager instanceof Arrow) {
            Arrow arrow = (Arrow) damager;
            if (arrow.getShooter() instanceof Player) {
                return ((Player) arrow.getShooter()).getName();
            }
        }

        return null;
    }

    /**
     * 获取武器名称（包含附魔信息）
     */
    public String getWeaponName(PlayerDeathEvent event) {
        Player player = event.getEntity();
        EntityDamageEvent damageEvent = player.getLastDamageCause();

        if (damageEvent == null) {
            return null;
        }

        if (!(damageEvent instanceof org.bukkit.event.entity.EntityDamageByEntityEvent)) {
            return null;
        }

        org.bukkit.event.entity.EntityDamageByEntityEvent entityEvent =
            (org.bukkit.event.entity.EntityDamageByEntityEvent) damageEvent;
        Entity damager = entityEvent.getDamager();

        // 玩家攻击
        if (damager instanceof Player) {
            Player killer = (Player) damager;
            ItemStack weapon = killer.getInventory().getItemInMainHand();

            if (weapon != null && weapon.getType() != Material.AIR) {
                // 如果武器有自定义名称，使用自定义名称（包含附魔）
                if (weapon.hasItemMeta() && weapon.getItemMeta().hasDisplayName()) {
                    return weapon.getItemMeta().displayName().toString();
                }

                // 构建武器名称（包含附魔信息）
                String weaponName = translateMaterialName(weapon.getType());

                // 添加附魔信息
                if (weapon.hasItemMeta() && weapon.getItemMeta().hasEnchants()) {
                    StringBuilder enchantInfo = new StringBuilder();
                    weapon.getItemMeta().getEnchants().forEach((enchant, level) -> {
                        if (enchantInfo.length() > 0) {
                            enchantInfo.append(" ");
                        }
                        enchantInfo.append(translateEnchantmentName(enchant, level));
                    });

                    if (enchantInfo.length() > 0) {
                        weaponName = enchantInfo + weaponName;
                    }
                }

                return weaponName;
            }

            return "拳头"; // 空手攻击
        }

        // 投射物攻击
        if (damager instanceof Arrow) {
            Arrow arrow = (Arrow) damager;
            if (arrow.getShooter() instanceof Player) {
                Player shooter = (Player) arrow.getShooter();
                ItemStack weapon = shooter.getInventory().getItemInMainHand();

                if (weapon != null && weapon.getType() == Material.BOW) {
                    String weaponName = "弓";

                    // 添加弓的附魔信息
                    if (weapon.hasItemMeta() && weapon.getItemMeta().hasEnchants()) {
                        StringBuilder enchantInfo = new StringBuilder();
                        weapon.getItemMeta().getEnchants().forEach((enchant, level) -> {
                            if (enchantInfo.length() > 0) {
                                enchantInfo.append(" ");
                            }
                            enchantInfo.append(translateEnchantmentName(enchant, level));
                        });

                        if (enchantInfo.length() > 0) {
                            weaponName = enchantInfo + weaponName;
                        }
                    }

                    return weaponName;
                } else if (weapon != null && weapon.getType() == Material.CROSSBOW) {
                    return "弩";
                }

                return "弓"; // 默认
            }
        }

        return null;
    }
    
    /**
     * 翻译附魔名称为中文
     */
    private String translateEnchantmentName(org.bukkit.enchantments.Enchantment enchant, int level) {
        String enchantName = "";

        // 根据附魔类型翻译
        switch (enchant.getKey().getKey()) {
            case "sharpness":
                enchantName = "锋利";
                break;
            case "smite":
                enchantName = "亡灵杀手";
                break;
            case "bane_of_arthropods":
                enchantName = "节肢杀手";
                break;
            case "knockback":
                enchantName = "击退";
                break;
            case "fire_aspect":
                enchantName = "火焰附加";
                break;
            case "looting":
                enchantName = "抢夺";
                break;
            case "sweeping":
                enchantName = "横扫之刃";
                break;
            case "power":
                enchantName = "力量";
                break;
            case "punch":
                enchantName = "冲击";
                break;
            case "flame":
                enchantName = "火矢";
                break;
            case "infinity":
                enchantName = "无限";
                break;
            case "unbreaking":
                enchantName = "耐久";
                break;
            case "mending":
                enchantName = "经验修补";
                break;
            default:
                enchantName = enchant.getKey().getKey();
                break;
        }

        // 添加等级（如果大于1）
        if (level > 1) {
            // 转换阿拉伯数字为罗马数字
            String romanLevel = toRoman(level);
            return enchantName + romanLevel;
        } else {
            return enchantName;
        }
    }

    /**
     * 转换数字为罗马数字
     */
    private String toRoman(int num) {
        switch (num) {
            case 1: return "";
            case 2: return "Ⅱ";
            case 3: return "Ⅲ";
            case 4: return "Ⅳ";
            case 5: return "Ⅴ";
            default: return String.valueOf(num);
        }
    }

    /**
     * 翻译材料名称为中文
     */
    private String translateMaterialName(Material material) {
        switch (material) {
            case WOODEN_SWORD:
                return "木剑";
            case STONE_SWORD:
                return "石剑";
            case IRON_SWORD:
                return "铁剑";
            case GOLDEN_SWORD:
                return "金剑";
            case DIAMOND_SWORD:
                return "钻石剑";
            case NETHERITE_SWORD:
                return "下界合金剑";
            case BOW:
                return "弓";
            case CROSSBOW:
                return "弩";
            case TRIDENT:
                return "三叉戟";
            case WOODEN_AXE:
                return "木斧";
            case STONE_AXE:
                return "石斧";
            case IRON_AXE:
                return "铁斧";
            case GOLDEN_AXE:
                return "金斧";
            case DIAMOND_AXE:
                return "钻石斧";
            case NETHERITE_AXE:
                return "下界合金斧";
            default:
                // 对于其他物品，返回英文名称的简化版本
                String name = material.name().toLowerCase().replace("_", " ");
                return name.substring(0, 1).toUpperCase() + name.substring(1);
        }
    }
}
