package org.plugin.messagetools.paper.util;

import org.bukkit.Material;
import org.bukkit.entity.*;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.inventory.ItemStack;

/**
 * 死亡原因分析器
 * 负责分析玩家死亡的具体原因
 */
public class DeathCauseAnalyzer {
    
    /**
     * 分析玩家死亡原因
     */
    public String analyzeDeath(PlayerDeathEvent event) {
        Player player = event.getEntity();
        EntityDamageEvent damageEvent = player.getLastDamageCause();
        
        if (damageEvent == null) {
            return "unknown";
        }
        
        EntityDamageEvent.DamageCause cause = damageEvent.getCause();
        
        // 根据伤害原因分析具体死亡类型
        switch (cause) {
            case FALL:
                return "fall";
            case DROWNING:
                return "drown";
            case LAVA:
                return "lava";
            case FIRE:
            case FIRE_TICK:
                return "fire";
            case SUFFOCATION:
                return "suffocation";
            case VOID:
                return "void";
            case STARVATION:
                return "starve";
            case POISON:
                return "poison";
            case MAGIC:
                return "magic";
            case WITHER:
                return "wither";
            case ENTITY_EXPLOSION:
                return "explosion";
            case BLOCK_EXPLOSION:
                return "tnt";
            case ENTITY_ATTACK:
                return analyzeEntityAttack(damageEvent);
            case PROJECTILE:
                return analyzeProjectileAttack(damageEvent);
            default:
                return "unknown";
        }
    }
    
    /**
     * 分析实体攻击类型
     */
    private String analyzeEntityAttack(EntityDamageEvent damageEvent) {
        if (!(damageEvent instanceof org.bukkit.event.entity.EntityDamageByEntityEvent)) {
            return "mob";
        }
        
        org.bukkit.event.entity.EntityDamageByEntityEvent entityEvent = 
            (org.bukkit.event.entity.EntityDamageByEntityEvent) damageEvent;
        Entity damager = entityEvent.getDamager();
        
        if (damager instanceof Player) {
            return "player";
        } else if (damager instanceof Zombie) {
            return "zombie";
        } else if (damager instanceof Skeleton) {
            return "skeleton";
        } else if (damager instanceof Spider) {
            return "spider";
        } else if (damager instanceof Creeper) {
            return "creeper";
        } else if (damager instanceof Enderman) {
            return "enderman";
        } else {
            return "mob";
        }
    }
    
    /**
     * 分析投射物攻击类型
     */
    private String analyzeProjectileAttack(EntityDamageEvent damageEvent) {
        if (!(damageEvent instanceof org.bukkit.event.entity.EntityDamageByEntityEvent)) {
            return "projectile";
        }
        
        org.bukkit.event.entity.EntityDamageByEntityEvent entityEvent = 
            (org.bukkit.event.entity.EntityDamageByEntityEvent) damageEvent;
        Entity projectile = entityEvent.getDamager();
        
        if (projectile instanceof Arrow) {
            Arrow arrow = (Arrow) projectile;
            if (arrow.getShooter() instanceof Player) {
                return "player_bow";
            } else if (arrow.getShooter() instanceof Skeleton) {
                return "skeleton";
            }
        }
        
        return "projectile";
    }
    
    /**
     * 获取武器名称
     */
    public String getWeaponName(PlayerDeathEvent event) {
        Player player = event.getEntity();
        EntityDamageEvent damageEvent = player.getLastDamageCause();
        
        if (damageEvent == null) {
            return null;
        }
        
        if (!(damageEvent instanceof org.bukkit.event.entity.EntityDamageByEntityEvent)) {
            return null;
        }
        
        org.bukkit.event.entity.EntityDamageByEntityEvent entityEvent = 
            (org.bukkit.event.entity.EntityDamageByEntityEvent) damageEvent;
        Entity damager = entityEvent.getDamager();
        
        // 玩家攻击
        if (damager instanceof Player) {
            Player killer = (Player) damager;
            ItemStack weapon = killer.getInventory().getItemInMainHand();
            
            if (weapon != null && weapon.getType() != Material.AIR) {
                // 如果武器有自定义名称，使用自定义名称
                if (weapon.hasItemMeta() && weapon.getItemMeta().hasDisplayName()) {
                    return weapon.getItemMeta().getDisplayName();
                }
                
                // 否则使用材料名称的中文翻译
                return translateMaterialName(weapon.getType());
            }
            
            return killer.getName(); // 空手攻击，返回玩家名
        }
        
        // 投射物攻击
        if (damager instanceof Arrow) {
            Arrow arrow = (Arrow) damager;
            if (arrow.getShooter() instanceof Player) {
                Player shooter = (Player) arrow.getShooter();
                return shooter.getName();
            }
        }
        
        return null;
    }
    
    /**
     * 翻译材料名称为中文
     */
    private String translateMaterialName(Material material) {
        switch (material) {
            case WOODEN_SWORD:
                return "木剑";
            case STONE_SWORD:
                return "石剑";
            case IRON_SWORD:
                return "铁剑";
            case GOLDEN_SWORD:
                return "金剑";
            case DIAMOND_SWORD:
                return "钻石剑";
            case NETHERITE_SWORD:
                return "下界合金剑";
            case BOW:
                return "弓";
            case CROSSBOW:
                return "弩";
            case TRIDENT:
                return "三叉戟";
            case WOODEN_AXE:
                return "木斧";
            case STONE_AXE:
                return "石斧";
            case IRON_AXE:
                return "铁斧";
            case GOLDEN_AXE:
                return "金斧";
            case DIAMOND_AXE:
                return "钻石斧";
            case NETHERITE_AXE:
                return "下界合金斧";
            default:
                // 对于其他物品，返回英文名称的简化版本
                String name = material.name().toLowerCase().replace("_", " ");
                return name.substring(0, 1).toUpperCase() + name.substring(1);
        }
    }
}
