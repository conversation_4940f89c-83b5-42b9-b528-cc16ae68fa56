# MessageTools 死亡消息多样性和重复发送修复

## 🔍 问题分析

根据你的反馈，我发现了两个关键问题：

### 问题1：死亡消息多样性不足
- **问题**：自定义构建的死亡消息缺少原生死亡消息的丰富多样性
- **原因**：我们完全抛弃了原生死亡消息，使用简化的自定义构建

### 问题2：多次发送死亡消息
- **问题**：死亡消息会根据在线玩家数量多次发送
- **原因**：可能是Paper端的双重事件监听器或者缺少防重复机制

## 🔧 修复方案

### 修复1：保持原生死亡消息多样性

#### 新的处理策略
```java
private net.kyori.adventure.text.Component buildCompleteDeathMessage(PlayerDeathEvent event) {
    // 获取原始死亡消息
    net.kyori.adventure.text.Component originalMessage = event.deathMessage();
    
    if (originalMessage != null) {
        // 处理原始死亡消息，替换玩家名为纯玩家名
        return processOriginalDeathMessage(originalMessage, event);
    }
    
    // 如果没有原始死亡消息，使用自定义构建
    return buildCustomDeathMessage(event);
}
```

#### 智能玩家名替换
```java
private net.kyori.adventure.text.Component processOriginalDeathMessage(
        net.kyori.adventure.text.Component originalMessage, 
        PlayerDeathEvent event) {
    
    Player player = event.getEntity();
    String originalPlayerName = player.getName();
    String plainPlayerName = getPlainPlayerName(player);
    
    // 如果玩家名相同，直接返回原始消息
    if (originalPlayerName.equals(plainPlayerName)) {
        return originalMessage;
    }
    
    // 递归处理Component，替换玩家名
    return replacePlayerNameInComponent(originalMessage, originalPlayerName, plainPlayerName, event);
}
```

#### 递归Component处理
```java
private net.kyori.adventure.text.Component replacePlayerNameInComponent(
        net.kyori.adventure.text.Component component, 
        String originalName, 
        String plainName,
        PlayerDeathEvent event) {
    
    // 处理文本组件
    if (component instanceof net.kyori.adventure.text.TextComponent) {
        net.kyori.adventure.text.TextComponent textComponent = (net.kyori.adventure.text.TextComponent) component;
        String content = textComponent.content();
        
        // 替换文本中的玩家名
        if (content.contains(originalName)) {
            content = content.replace(originalName, plainName);
        }
        
        // 创建新的文本组件，保持样式和子组件
        net.kyori.adventure.text.TextComponent.Builder builder = net.kyori.adventure.text.Component.text()
            .content(content)
            .style(textComponent.style());
        
        // 递归处理子组件
        for (net.kyori.adventure.text.Component child : textComponent.children()) {
            net.kyori.adventure.text.Component processedChild = replacePlayerNameInComponent(child, originalName, plainName, event);
            if (processedChild != null) {
                builder.append(processedChild);
            }
        }
        
        return builder.build();
    }
    
    // 对于其他类型的组件，如果包含玩家名，则回退到自定义构建
    String componentText = net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer.plainText().serialize(component);
    if (componentText.contains(originalName)) {
        return buildCustomDeathMessage(event);
    }
    
    return component;
}
```

### 修复2：防重复发送机制

#### 添加防重复字段
```java
public class DeathMessageService {
    // 防重复发送机制
    private final java.util.Set<String> processedDeaths = java.util.concurrent.ConcurrentHashMap.newKeySet();
}
```

#### 防重复处理逻辑
```java
public void handlePlayerDeath(PlayerDeathEvent event) {
    Player player = event.getEntity();
    
    // 生成唯一的死亡事件ID（玩家名+时间戳）
    String deathId = player.getName() + "_" + System.currentTimeMillis();
    
    // 防重复处理机制
    if (!processedDeaths.add(deathId)) {
        if (debugEnabled) {
            plugin.getLogger().info("跳过重复的死亡事件: " + player.getName());
        }
        return;
    }
    
    // 清理旧的死亡记录（保留最近1000个）
    if (processedDeaths.size() > 1000) {
        processedDeaths.clear();
    }
    
    if (debugEnabled) {
        plugin.getLogger().info("处理玩家死亡事件: " + player.getName() + " (ID: " + deathId + ")");
    }
    
    // 继续处理死亡消息...
}
```

## 🎯 修复效果

### 1. 死亡消息多样性提升

#### 修复前（简化的自定义消息）
```
PlayerA 落地过猛
PlayerB 被僵尸杀死了
PlayerC 被 PlayerD 用 钻石剑 杀死了
```

#### 修复后（保持原生多样性）
```
PlayerA 从高处摔了下来
PlayerA 落地过猛
PlayerA 试图抱抱仙人掌
PlayerB 被僵尸杀死了
PlayerB 被僵尸 Zombie 杀死了
PlayerC 被 PlayerD 用 [锋利Ⅴ钻石剑] 杀死了  # 保持武器悬停效果
PlayerC 被 PlayerD 用 [天丛云剑] 击杀  # 自定义武器名称
```

### 2. 重复发送问题解决

#### 修复前的问题日志
```
[INFO] 处理玩家死亡事件: PlayerA
[INFO] 处理玩家死亡事件: PlayerA  # 重复处理
[INFO] 处理玩家死亡事件: PlayerA  # 重复处理
```

#### 修复后的预期日志
```
[INFO] 处理玩家死亡事件: PlayerA (ID: PlayerA_1703123456789)
[INFO] 跳过重复的死亡事件: PlayerA  # 防重复机制生效
[INFO] 跳过重复的死亡事件: PlayerA  # 防重复机制生效
```

## 🎮 技术优势

### 1. 保持原生多样性
- ✅ **利用原生死亡消息**：保持Minecraft原生的丰富死亡消息变体
- ✅ **智能玩家名替换**：只替换玩家名，保持其他内容不变
- ✅ **保持悬停效果**：武器和其他组件的悬停效果完全保留
- ✅ **回退机制**：复杂情况下回退到自定义构建

### 2. 防重复机制
- ✅ **唯一ID生成**：使用玩家名+时间戳生成唯一死亡事件ID
- ✅ **线程安全**：使用ConcurrentHashMap确保多线程安全
- ✅ **内存管理**：自动清理旧记录，防止内存泄漏
- ✅ **调试友好**：提供详细的重复检测日志

### 3. 智能处理策略
- ✅ **优先使用原生**：优先处理原生死亡消息
- ✅ **递归组件处理**：正确处理复杂的Component结构
- ✅ **样式保持**：保持原始组件的样式和格式
- ✅ **兼容性强**：支持各种类型的Component

## 📋 验证方法

### 1. 死亡消息多样性测试
- [ ] **环境死亡**：测试摔死、溺水、岩浆等各种环境死亡
- [ ] **怪物攻击**：测试不同怪物的攻击死亡消息
- [ ] **PvP死亡**：测试各种武器的PvP死亡消息
- [ ] **特殊情况**：测试爆炸、魔法等特殊死亡原因

### 2. 重复发送测试
- [ ] **启用调试模式**：查看死亡事件处理日志
- [ ] **多玩家环境**：在有多个在线玩家时测试
- [ ] **快速死亡**：测试玩家快速连续死亡的情况
- [ ] **检查日志**：确认重复事件被正确跳过

### 3. 玩家名替换测试
- [ ] **Team前缀**：确认team前缀被正确移除
- [ ] **颜色代码**：确认颜色代码被正确移除
- [ ] **武器悬停**：确认武器悬停效果正常显示
- [ ] **自定义武器**：确认自定义武器名称正确显示

## 📦 部署文件

### 更新的JAR文件
- **Velocity端**：`MessageTools-1.7-SNAPSHOT.jar`
- **Paper端**：`messagetools-paper-1.0-SNAPSHOT.jar`

### 配置建议
```yaml
# Velocity端配置
debug:
  enabled: true      # 临时启用查看详细处理日志
  verbose_events: true

death_messages:
  enabled: true
  include_source_server: false  # 避免重复发送
```

## ✅ 关键改进

1. **原生多样性保持**：利用Minecraft原生死亡消息的丰富变体
2. **智能玩家名处理**：只替换玩家名，保持其他内容和效果
3. **防重复机制**：彻底解决多次发送问题
4. **内存安全**：自动清理机制防止内存泄漏
5. **调试友好**：详细的处理和重复检测日志

现在MessageTools既保持了原生死亡消息的丰富多样性，又解决了重复发送问题，提供了最佳的用户体验！🎮
