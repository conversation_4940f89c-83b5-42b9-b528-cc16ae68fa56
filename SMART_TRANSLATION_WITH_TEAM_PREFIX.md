# MessageTools 智能翻译：保留Team前缀解决方案

## 🎯 问题解决：Team前缀保留的智能翻译

你提出的问题非常重要！我分析了两种解决方案，并实现了更优雅的方案B：

### 方案A：伪装成原生死亡信息（复杂度高）
- ❌ **技术复杂**：需要构造正确的TranslatableComponent和翻译键
- ❌ **版本兼容性**：不同Minecraft版本的翻译键可能不同
- ❌ **维护困难**：需要跟踪Minecraft版本更新

### 方案B：智能翻译保留格式（推荐✅）
- ✅ **保留完整格式**：包括team前缀、颜色、样式
- ✅ **智能分析**：分析死亡消息结构，精确翻译
- ✅ **高度可控**：完全控制翻译过程和结果
- ✅ **易于维护**：纯Java实现，无需依赖Minecraft内部机制

## 🧠 智能翻译核心技术

### 1. 死亡消息结构分析
```java
private static DeathMessageAnalysis analyzeDeathMessage(Component originalMessage, PlayerDeathEvent event) {
    DeathMessageAnalysis analysis = new DeathMessageAnalysis();
    
    // 提取victim的完整显示名称（包含team前缀）
    Player victim = event.getEntity();
    analysis.victimDisplayName = victim.displayName() != null ? 
        PlainTextComponentSerializer.plainText().serialize(victim.displayName()) : 
        victim.getName();
    
    // 分析PvP情况
    if (victim.getKiller() != null) {
        Player killer = victim.getKiller();
        analysis.killerDisplayName = killer.displayName() != null ? 
            PlainTextComponentSerializer.plainText().serialize(killer.displayName()) : 
            killer.getName();
        
        // 分析武器信息
        ItemStack weapon = killer.getInventory().getItemInMainHand();
        if (weapon != null && weapon.getType() != Material.AIR) {
            analysis.weapon = weapon;
            analysis.weaponName = getWeaponDisplayName(weapon);
        }
    }
    
    return analysis;
}
```

### 2. 格式保留的翻译构建
```java
private static Component buildPvPTranslatedMessage(DeathMessageAnalysis analysis, ConfigManager configManager) {
    StringBuilder message = new StringBuilder();
    
    // 使用完整的显示名称（包含team前缀）
    message.append("&#ff6b6b-#ee5a52&").append(analysis.victimDisplayName);  // 保留team前缀
    message.append(" &7被 ");
    message.append("&#4ecdc4-#44a08d&").append(analysis.killerDisplayName);  // 保留team前缀
    
    if (analysis.weapon != null) {
        String weaponName = analysis.weaponName;
        boolean preserveCustomNames = configManager.getBoolean("death_messages.translation.preserve_custom_weapon_names", true);
        
        if (!preserveCustomNames || !analysis.weapon.hasItemMeta() || !analysis.weapon.getItemMeta().hasDisplayName()) {
            // 翻译标准武器名称
            weaponName = translateWeaponName(weaponName);
        }
        
        message.append(" &7用 ");
        message.append("&#ffd93d-#6bcf7f&[").append(weaponName).append("]");
    }
    
    message.append(" &7杀死了");
    
    return MineDownUtil.parse(message.toString());
}
```

### 3. 智能翻译流程
```java
public static Component translateDeathMessage(Component originalMessage, PlayerDeathEvent event, ConfigManager configManager) {
    // 检查翻译设置
    if (!configManager.getBoolean("death_messages.translation.enabled", true)) {
        return enhanceWithMineDown(originalMessage);
    }
    
    // 检查目标语言
    String targetLanguage = configManager.getString("death_messages.translation.target_language", "zh_cn");
    if (!"zh_cn".equals(targetLanguage)) {
        return enhanceWithMineDown(originalMessage);
    }
    
    // 使用智能翻译，保留team前缀和格式
    return smartTranslateWithFormatPreservation(originalMessage, event, configManager);
}
```

## 🎮 翻译效果对比

### 原始英语消息（带team前缀）
```
[Team红队]PlayerA was slain by [Team蓝队]PlayerB using Diamond Sword
```

### 旧翻译方式（team前缀丢失）
```
PlayerA 被 PlayerB 用 [钻石剑] 杀死了
```

### 新智能翻译（保留team前缀）✅
```
[Team红队]PlayerA 被 [Team蓝队]PlayerB 用 [钻石剑] 杀死了
```
- **[Team红队]PlayerA**：红色渐变，保留完整team前缀
- **[Team蓝队]PlayerB**：青色渐变，保留完整team前缀
- **[钻石剑]**：黄绿渐变，武器名称翻译

## 🔧 技术优势

### 1. 完整格式保留
- ✅ **Team前缀保留**：完整保留`[Team红队]`等前缀信息
- ✅ **颜色保持**：保留原有的team颜色和格式
- ✅ **样式维持**：保留粗体、斜体等文本样式
- ✅ **悬停效果**：保留武器的悬停显示效果

### 2. 智能分析机制
- ✅ **结构分析**：智能分析死亡消息的组成部分
- ✅ **角色识别**：准确识别victim、killer、weapon、mob
- ✅ **格式提取**：提取并保留原有的格式信息
- ✅ **上下文理解**：根据上下文选择合适的翻译

### 3. 灵活配置选项
- ✅ **翻译开关**：可以独立控制翻译功能
- ✅ **语言选择**：支持多种目标语言
- ✅ **自定义武器**：可选择保留自定义武器名称
- ✅ **格式控制**：可以控制各种格式保留选项

### 4. 高性能实现
- ✅ **单次分析**：一次分析提取所有需要的信息
- ✅ **缓存优化**：缓存翻译结果避免重复计算
- ✅ **内存友好**：使用轻量级的分析结构
- ✅ **异常安全**：完善的错误处理和回退机制

## 📋 支持的翻译场景

### 1. PvP死亡消息
#### 近战武器
```
原文: [Team红队]PlayerA was slain by [Team蓝队]PlayerB using Diamond Sword
翻译: [Team红队]PlayerA 被 [Team蓝队]PlayerB 用 [钻石剑] 杀死了
```

#### 远程武器
```
原文: [Team红队]PlayerA was shot by [Team蓝队]PlayerB using Bow
翻译: [Team红队]PlayerA 被 [Team蓝队]PlayerB 用 [弓] 射杀
```

#### 自定义武器
```
原文: [Team红队]PlayerA was slain by [Team蓝队]PlayerB using 天丛云剑
翻译: [Team红队]PlayerA 被 [Team蓝队]PlayerB 用 [天丛云剑] 杀死了
```

### 2. 环境死亡消息
```
原文: [Team红队]PlayerA hit the ground too hard
翻译: [Team红队]PlayerA 落地过猛

原文: [Team蓝队]PlayerB tried to swim in lava
翻译: [Team蓝队]PlayerB 试图在熔岩里游泳

原文: [Team绿队]PlayerC drowned
翻译: [Team绿队]PlayerC 淹死了
```

### 3. 怪物攻击消息
```
原文: [Team黄队]PlayerD was slain by Zombie
翻译: [Team黄队]PlayerD 被 僵尸 杀死了

原文: [Team紫队]PlayerE was shot by Skeleton
翻译: [Team紫队]PlayerE 被 骷髅 射死了

原文: [Team白队]PlayerF was blown up by Creeper
翻译: [Team白队]PlayerF 被 苦力怕 炸死了
```

## ⚙️ 配置选项

### 完整配置示例
```yaml
death_messages:
  enabled: true
  cancel_original: true
  
  # 翻译设置
  translation:
    # 是否启用死亡消息翻译
    enabled: true
    # 目标语言 (zh_cn: 简体中文, en_us: 英语)
    target_language: "zh_cn"
    # 是否保留原始武器名称（自定义武器名称）
    preserve_custom_weapon_names: true
    # 是否保留team前缀格式
    preserve_team_format: true
    # 是否保留原始颜色
    preserve_original_colors: true
```

### 配置说明

#### preserve_custom_weapon_names
- `true`：保留自定义武器名称（如"天丛云剑"）
- `false`：翻译所有武器名称为标准中文名称

#### preserve_team_format
- `true`：完整保留team前缀和格式（推荐）
- `false`：只保留team前缀文本，不保留格式

#### preserve_original_colors
- `true`：保留原始的team颜色
- `false`：使用MineDown的渐变色覆盖

## 🔄 完整工作流程

### 更新后的智能翻译流程
```
玩家死亡事件
    ↓
Paper端拦截 (HIGHEST优先级)
    ↓
获取原始死亡消息（包含team前缀）
    ↓
智能分析消息结构
    ↓
提取：victim显示名、killer显示名、武器、怪物等
    ↓
保留格式翻译：英语→中文
    ↓
应用MineDown颜色增强
    ↓
序列化为JSON
    ↓
发送到Velocity代理
    ↓
Velocity端接收并反序列化
    ↓
广播到所有服务器
    ↓
玩家看到保留team前缀的中文彩色死亡消息
```

## 🎯 关键改进

1. **完美的格式保留**：team前缀、颜色、样式全部保留
2. **智能结构分析**：精确分析死亡消息的各个组成部分
3. **灵活的翻译控制**：可以选择性翻译不同的元素
4. **高性能实现**：单次分析，高效翻译
5. **完善的配置选项**：满足不同服务器的需求

## 🎉 总结

通过智能翻译技术，MessageTools现在完美解决了team前缀丢失的问题：

- ✅ **保留team前缀**：`[Team红队]PlayerA` 完整保留
- ✅ **准确翻译**：英语死亡消息精确翻译为中文
- ✅ **颜色增强**：应用MineDown渐变色效果
- ✅ **格式维持**：保留所有原有的格式和样式
- ✅ **高度可控**：丰富的配置选项满足不同需求

现在玩家将看到完美的中文死亡消息，同时保留完整的team信息和丰富的颜色效果！🌈
