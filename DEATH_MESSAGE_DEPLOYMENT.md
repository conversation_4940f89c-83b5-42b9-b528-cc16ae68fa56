# MessageTools 死亡消息功能部署指南

## 🎯 功能概述

MessageTools v1.8 新增了死亡消息全服通报功能，通过Velocity代理实现跨服务器的死亡消息广播。当任何Paper服务器上的玩家死亡时，死亡消息会自动发送到所有连接的服务器。

## 📦 所需组件

### 1. Velocity端 (代理服务器)
- **MessageTools-1.8-SNAPSHOT.jar** - 主插件
- 处理死亡消息广播
- 格式化和发送消息到所有玩家

### 2. Paper端 (游戏服务器)
- **MessageTools-Paper-1.0-SNAPSHOT.jar** - Paper端插件
- 监听死亡事件
- 分析死亡原因
- 发送数据到Velocity代理

## 🚀 部署步骤

### 第一步：部署Velocity端

1. **安装主插件**
   ```
   将 MessageTools-1.8-SNAPSHOT.jar 放入 Velocity/plugins/ 目录
   ```

2. **配置死亡消息**
   编辑 `plugins/MessageTools/config.yml`：
   ```yaml
   death_messages:
     enabled: true
     broadcast_to_all_servers: true
     include_source_server: true
     console_output: true
     default_format: "&c{unicode:skull} &f%player% &7死亡了"
     
     formats:
       # 自然死亡
       fall: "&c{unicode:skull} &f%player% &7摔死了"
       drown: "&c{unicode:skull} &f%player% &7溺水身亡"
       lava: "&c{unicode:fire} &f%player% &7被岩浆烧死了"
       fire: "&c{unicode:fire} &f%player% &7被烧死了"
       void: "&c{unicode:skull} &f%player% &7掉入虚空"
       
       # 玩家PvP
       player: "&c{unicode:sword} &f%player% &7被 &c%weapon% &7杀死了"
       player_bow: "&c{unicode:bow} &f%player% &7被 &c%weapon% &7射死了"
       
       # 生物攻击
       creeper: "&c{unicode:explosion} &f%player% &7被苦力怕炸死了"
       zombie: "&c{unicode:skull} &f%player% &7被僵尸杀死了"
       skeleton: "&c{unicode:skull} &f%player% &7被骷髅射死了"
       spider: "&c{unicode:skull} &f%player% &7被蜘蛛杀死了"
       enderman: "&c{unicode:skull} &f%player% &7被末影人杀死了"
       
       # 爆炸
       explosion: "&c{unicode:explosion} &f%player% &7被炸死了"
       tnt: "&c{unicode:explosion} &f%player% &7被TNT炸死了"
   ```

3. **重启Velocity服务器**

### 第二步：部署Paper端

1. **安装Paper端插件**
   ```
   将 MessageTools-Paper-1.0-SNAPSHOT.jar 放入每个Paper服务器的 plugins/ 目录
   ```

2. **配置Paper端**
   编辑 `plugins/MessageTools-Paper/config.yml`：
   ```yaml
   death_messages:
     enabled: true
     cancel_original: true  # 取消原始死亡消息，避免重复
   
   weapon_translations:
     diamond_sword: "钻石剑"
     iron_sword: "铁剑"
     bow: "弓"
     crossbow: "弩"
     # 更多武器翻译...
   
   debug:
     enabled: false  # 生产环境关闭调试
   ```

3. **重启所有Paper服务器**

## 🔧 配置详解

### Velocity端配置选项

```yaml
death_messages:
  # 是否启用死亡消息功能
  enabled: true
  
  # 是否广播到所有服务器
  broadcast_to_all_servers: true
  
  # 是否包含来源服务器的玩家
  include_source_server: true
  
  # 是否在控制台输出死亡消息
  console_output: true
  
  # 默认死亡消息格式（当没有匹配的死亡原因时使用）
  default_format: "&c{unicode:skull} &f%player% &7死亡了"
  
  # 各种死亡原因的消息格式
  formats:
    # 死亡原因: "消息格式"
```

### Paper端配置选项

```yaml
death_messages:
  # 是否启用死亡消息功能
  enabled: true
  
  # 是否取消原始的死亡消息（推荐启用，避免重复显示）
  cancel_original: true

# 武器名称中文翻译
weapon_translations:
  # 英文名称: "中文名称"
  diamond_sword: "钻石剑"
  bow: "弓"
```

## 🎮 支持的死亡原因

### 自然环境死亡
- **fall** - 摔死
- **drown** - 溺水身亡
- **lava** - 被岩浆烧死
- **fire** - 被火烧死
- **suffocation** - 窒息而死
- **void** - 掉入虚空
- **starve** - 饿死

### 玩家对战 (PvP)
- **player** - 被玩家近战攻击杀死
- **player_bow** - 被玩家弓箭射死

### 生物攻击
- **mob** - 被一般怪物杀死
- **zombie** - 被僵尸杀死
- **skeleton** - 被骷髅射死
- **spider** - 被蜘蛛杀死
- **creeper** - 被苦力怕炸死
- **enderman** - 被末影人杀死

### 爆炸伤害
- **explosion** - 被爆炸炸死
- **tnt** - 被TNT炸死

### 魔法伤害
- **magic** - 被魔法杀死
- **poison** - 中毒身亡
- **wither** - 被凋零杀死

## 🎨 消息格式变量

### 可用变量
- **%player%** - 死亡玩家的用户名
- **%weapon%** - 杀死玩家的武器名称（仅PvP时有效）
- **%server%** - 死亡发生的服务器名称

### Unicode字符支持
可以在死亡消息中使用Unicode字符：
- `{unicode:skull}` → 💀
- `{unicode:sword}` → ⚔
- `{unicode:bow}` → 🏹
- `{unicode:fire}` → 🔥
- `{unicode:explosion}` → 💥

## 📊 实际效果演示

### PvP死亡消息
```
⚔ PlayerA 被 钻石剑 杀死了
🏹 PlayerB 被 PlayerC 射死了
```

### 环境死亡消息
```
💀 PlayerD 摔死了
🔥 PlayerE 被岩浆烧死了
💀 PlayerF 溺水身亡
```

### 怪物攻击消息
```
💥 PlayerG 被苦力怕炸死了
💀 PlayerH 被僵尸杀死了
💀 PlayerI 被骷髅射死了
```

## 🔍 测试和验证

### 1. 基础功能测试
1. 在任意Paper服务器中让玩家死亡
2. 检查所有服务器是否显示死亡消息
3. 验证消息格式是否正确

### 2. PvP测试
1. 让两个玩家进行PvP
2. 检查是否显示武器信息
3. 验证杀手和被杀者信息

### 3. 不同死亡原因测试
1. 测试摔死、溺水、岩浆等自然死亡
2. 测试怪物攻击死亡
3. 测试爆炸死亡

### 4. 调试模式测试
启用调试模式查看详细信息：
```yaml
debug:
  enabled: true
  verbose_events: true
```

## 🔧 故障排除

### 问题1：死亡消息不显示
**可能原因**：
- Velocity端未启用死亡消息功能
- Paper端插件未正确安装
- 插件消息通道通信失败

**解决方案**：
1. 检查Velocity端配置：`death_messages.enabled: true`
2. 检查Paper端配置：`death_messages.enabled: true`
3. 查看控制台日志确认插件正常加载
4. 重启所有服务器

### 问题2：消息格式错误
**可能原因**：
- 配置文件语法错误
- Unicode字符配置错误
- 变量名称错误

**解决方案**：
1. 检查YAML语法是否正确
2. 验证Unicode字符名称
3. 确认变量名称拼写正确

### 问题3：只在部分服务器显示
**可能原因**：
- 部分Paper服务器未安装Paper端插件
- 网络连接问题
- 插件版本不匹配

**解决方案**：
1. 确保所有Paper服务器都安装了Paper端插件
2. 检查网络连接
3. 使用相同版本的插件

### 问题4：武器名称显示为英文
**可能原因**：
- Paper端未配置武器翻译
- 武器名称映射缺失

**解决方案**：
1. 在Paper端配置文件中添加武器翻译
2. 重启Paper服务器

## 📈 性能考虑

### 网络开销
- 每次死亡约发送50-100字节数据
- 对网络带宽影响极小

### 服务器性能
- 死亡事件处理为异步执行
- 不影响游戏性能
- 支持高频死亡事件

### 内存使用
- Velocity端增加约1MB内存使用
- Paper端增加约500KB内存使用

## 🎯 高级配置

### 自定义死亡原因
可以添加更多死亡原因：
```yaml
formats:
  dragon: "&c🐉 &f%player% &7被末影龙杀死了"
  warden: "&c👁 &f%player% &7被监守者杀死了"
```

### 服务器特定消息
可以在消息中显示服务器名称：
```yaml
formats:
  player: "&c[%server%] &f%player% &7被 &c%weapon% &7杀死了"
```

### 条件消息
可以根据不同条件显示不同消息：
```yaml
# 在Paper端可以通过权限或其他条件自定义消息处理
```

---

通过这个死亡消息全服通报功能，你的Minecraft服务器网络现在可以实现真正的跨服务器死亡消息同步，让所有玩家都能及时了解网络中发生的重要事件！
