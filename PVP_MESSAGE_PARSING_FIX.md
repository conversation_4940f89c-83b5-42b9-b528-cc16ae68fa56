# MessageTools PvP消息解析修复

## 🔍 问题分析

从日志中发现的关键问题：

```
[16:04:54 INFO] [messagetools]: 收到来自服务器 newnanserver 的死亡消息: viagar|player_weapon|NSrank|TextComponentImpl{content="", style=StyleImpl{...}, children=[TextComponentImpl{content="天丛云剑", style=StyleImpl{...}}]}
[16:04:54 INFO] [messagetools]: [死亡消息] viagar 死了
```

### 问题1：武器信息是TextComponent对象
- **问题**：Paper端发送的武器信息是Adventure TextComponent序列化数据
- **现象**：`TextComponentImpl{content="天丛云剑"...}` 而不是纯文本 `天丛云剑`
- **影响**：Velocity端无法正确解析武器名称

### 问题2：消息格式段数不匹配
- **问题**：Paper端发送4段数据，Velocity端期望5段数据
- **现象**：服务器名称缺失，导致解析错误
- **影响**：消息格式匹配失败

### 问题3：空手攻击显示"拳头"
- **问题**：空手攻击时返回"拳头"而不是null
- **现象**：`玩家B被拳头击杀` 而不是 `玩家B被玩家A杀死了`
- **影响**：不符合原版死亡消息格式

### 问题4：死亡消息格式匹配失败
- **问题**：`player_weapon` 格式无法正确匹配
- **现象**：最终显示默认格式 `viagar 死了`
- **影响**：PvP死亡消息不显示杀手和武器信息

## 🔧 修复方案

### 1. Paper端修复

#### DeathCauseAnalyzer.java
```java
// 修复武器名称提取 - 将TextComponent转换为纯文本
if (weapon.hasItemMeta() && weapon.getItemMeta().hasDisplayName()) {
    net.kyori.adventure.text.Component displayName = weapon.getItemMeta().displayName();
    if (displayName != null) {
        return net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer.plainText().serialize(displayName);
    }
}

// 修复空手攻击 - 返回null而不是"拳头"
return null; // 空手攻击，不返回武器名称
```

#### DeathMessageService.java
```java
// 修复消息格式 - 添加服务器名称
String serverName = plugin.getServer().getName();
if (serverName == null || serverName.isEmpty()) {
    serverName = "unknown";
}

// 构建5段格式消息
String messageData = playerName + "|" + deathCause + "|" + 
                   (killerName != null ? killerName : "") + "|" + 
                   (weapon != null ? weapon : "") + "|" + serverName;
```

### 2. Velocity端修复

#### DeathMessageService.java
```java
// 增强调试日志
if (debugEnabled) {
    logger.info("构建死亡消息 - 原因: {}, 格式: {}", deathCause, format);
    logger.info("变量 - 玩家: {}, 杀手: {}, 武器: {}", playerName, killerName, weapon);
}

// 改进消息解析
String sourceServer = parts.length > 4 && !parts[4].isEmpty() ? parts[4] : "未知服务器";

if (debugEnabled && verboseEvents) {
    logger.info("解析插件消息 - 段数: {}", parts.length);
    logger.info("  玩家: {}", playerName);
    logger.info("  死亡原因: {}", deathCause);
    logger.info("  杀手: {}", killerName);
    logger.info("  武器: {}", weapon);
    logger.info("  服务器: {}", sourceServer);
}
```

## 🎯 预期修复效果

### 修复前的问题
```
收到消息: viagar|player_weapon|NSrank|TextComponentImpl{content="天丛云剑"...}
输出结果: viagar 死了
```

### 修复后的预期
```
收到消息: viagar|player_weapon|NSrank|天丛云剑|newnanserver
输出结果: viagar 被 NSrank 用 天丛云剑 杀死了
```

## 🔍 调试信息增强

修复后将提供详细的调试日志：

```
[INFO] 解析插件消息 - 段数: 5
[INFO]   玩家: viagar
[INFO]   死亡原因: player_weapon
[INFO]   杀手: NSrank
[INFO]   武器: 天丛云剑
[INFO]   服务器: newnanserver
[INFO] 查找死亡格式 - 路径: death_messages.formats.player_weapon, 结果: &f%player% &7被 &f%killer% &7用 &f%weapon% &7杀死了
[INFO] 构建死亡消息 - 原因: player_weapon, 格式: &f%player% &7被 &f%killer% &7用 &f%weapon% &7杀死了
[INFO] 变量 - 玩家: viagar, 杀手: NSrank, 武器: 天丛云剑
[INFO] 最终死亡消息: viagar 被 NSrank 用 天丛云剑 杀死了
```

## 📋 测试验证

### 1. 空手PvP测试
- **预期**：`PlayerA 被 PlayerB 杀死了`
- **验证**：杀手名称正确显示，无武器信息

### 2. 武器PvP测试
- **预期**：`PlayerA 被 PlayerB 用 钻石剑 杀死了`
- **验证**：杀手和武器信息都正确显示

### 3. 自定义武器测试
- **预期**：`PlayerA 被 PlayerB 用 天丛云剑 杀死了`
- **验证**：自定义武器名称正确提取和显示

### 4. 附魔武器测试
- **预期**：`PlayerA 被 PlayerB 用 锋利Ⅴ钻石剑 杀死了`
- **验证**：附魔信息正确显示

### 5. 弓箭PvP测试
- **预期**：`PlayerA 被 PlayerB 用 力量Ⅴ弓 射杀`
- **验证**：远程武器PvP正确处理

## 🚀 部署说明

1. **重新编译**：两个项目都需要重新编译
2. **替换JAR文件**：
   - Velocity端：`MessageTools-1.7-SNAPSHOT.jar`
   - Paper端：`messagetools-paper-1.0-SNAPSHOT.jar`
3. **启用调试**：在配置文件中设置 `debug.enabled: true` 来查看详细日志
4. **测试验证**：进行PvP测试确认修复效果

## ✅ 关键改进

1. **TextComponent处理**：正确将Adventure组件转换为纯文本
2. **消息格式统一**：Paper端和Velocity端使用一致的5段格式
3. **空手攻击修复**：空手攻击不再显示"拳头"
4. **调试信息完善**：提供详细的消息解析和格式匹配日志
5. **错误处理增强**：更好的错误提示和异常处理

现在MessageTools应该能够正确处理所有PvP死亡消息，包括自定义武器名称和附魔信息！🎮
