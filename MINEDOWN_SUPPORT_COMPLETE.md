# MessageTools MineDown颜色支持完成

## 🎨 新策略：保留Team前缀 + MineDown颜色增强

根据你的建议，我们采用了全新的策略：
- ✅ **保留team前缀**：不再强制移除team前缀，保持原有的团队显示
- ✅ **MineDown颜色增强**：使用MineDown的丰富颜色格式来美化死亡消息
- ✅ **支持所有MineDown格式**：完整支持MineDown文档中的所有颜色代码

## 🎯 支持的MineDown颜色格式

### 1. 传统颜色代码
```
&6Text          # 金色文本
&cText          # 红色文本
&aText          # 绿色文本
```

### 2. 颜色名称
```
&gold&Text      # 金色
&red&Text       # 红色
&green&Text     # 绿色
```

### 3. RGB十六进制颜色
```
&#ff00ff&Text   # 完整十六进制格式
&#f0f&Text      # 短格式（等同于上面）
```

### 4. 渐变色
```
&#f0f-#fff&Text     # 两色渐变
&#fff-#333-#222&Text # 三色渐变
```

### 5. 彩虹色
```
&rainbow&Text       # 标准彩虹
&rainbow:20&Text    # 带相位的彩虹
```

### 6. 格式化
```
**Text**        # 粗体
##Text##        # 斜体
__Text__        # 下划线
~~Text~~        # 删除线
??Text??        # 混淆
```

## 🔧 技术实现

### Paper端实现

#### 1. MineDown工具类
```java
public class MineDownUtil {
    // 基础解析
    public static Component parse(String text);
    
    // 带占位符替换的解析
    public static Component parse(String text, Map<String, Object> replacements);
    
    // 检查是否包含MineDown格式
    public static boolean containsMineDownFormat(String text);
    
    // 死亡消息格式化
    public static Component formatDeathMessage(String playerName, String killerName, String weaponName, String deathCause);
    
    // 高级死亡消息格式化（渐变色）
    public static Component formatAdvancedDeathMessage(String playerName, String killerName, String weaponName, String deathCause);
}
```

#### 2. 增强的死亡消息处理
```java
private Component buildCompleteDeathMessage(PlayerDeathEvent event) {
    Component originalMessage = event.deathMessage();
    
    if (originalMessage != null) {
        // 使用MineDown增强原始死亡消息的颜色效果
        return enhanceDeathMessageWithMineDown(originalMessage, event);
    }
    
    // 如果没有原始死亡消息，使用MineDown自定义构建
    return buildMineDownDeathMessage(event);
}
```

#### 3. 智能颜色增强
```java
private Component enhanceDeathMessageWithMineDown(Component originalMessage, PlayerDeathEvent event) {
    // 将原始消息转换为纯文本
    String plainText = PlainTextComponentSerializer.plainText().serialize(originalMessage);
    
    // 根据死亡类型应用不同的MineDown格式
    if (isPvPDeath(event)) {
        return applyPvPMineDownFormat(plainText, victim, killer);
    } else if (isMobDeath(event)) {
        return applyMobMineDownFormat(plainText, damager);
    } else {
        return applyEnvironmentMineDownFormat(plainText, damageEvent);
    }
}
```

### Velocity端实现

#### 1. MineDown工具类
```java
public class MineDownUtil {
    // 基础功能
    public static Component parse(String text);
    public static Component formatMessage(String message);
    
    // 特殊效果
    public static Component formatRainbowDeathMessage(String playerName, String message);
    public static Component formatGradientDeathMessage(String playerName, String message, String startColor, String endColor);
    
    // 死亡消息格式化
    public static Component formatPvPDeathMessage(String victimName, String killerName, String weaponName);
    public static Component formatEnvironmentDeathMessage(String playerName, String deathCause);
    public static Component formatMobDeathMessage(String playerName, String mobType);
    
    // 效果应用
    public static Component applyEffect(String text, String effect);
}
```

## 🎮 死亡消息效果展示

### 1. PvP死亡消息
#### 传统格式
```
[Team红队]PlayerA 被 [Team蓝队]PlayerB 用 钻石剑 杀死了
```

#### MineDown增强后
```
[Team红队]PlayerA 被 [Team蓝队]PlayerB 用 [钻石剑] 杀死了
```
- **PlayerA**：红色到深红色渐变 `&#ff6b6b-#ee5a52&`
- **PlayerB**：青色到深青色渐变 `&#4ecdc4-#44a08d&`
- **武器**：黄色到绿色渐变 `&#ffd93d-#6bcf7f&`

### 2. 环境死亡消息
#### 摔死
```
[Team红队]PlayerA 落地过猛
```
- **PlayerA**：粉色渐变 `&#ff9a9e-#fecfef&`

#### 溺水
```
[Team蓝队]PlayerB 淹死了
```
- **PlayerB**：蓝粉渐变 `&#a8edea-#fed6e3&`

#### 岩浆
```
[Team黄队]PlayerC 试图在熔岩里游泳
```
- **PlayerC**：橙色渐变 `&#ff9a56-#ff6b35&`

### 3. 怪物攻击死亡消息
#### 僵尸攻击
```
[Team绿队]PlayerD 被僵尸杀死了
```
- **PlayerD**：红色渐变 `&#ff6b6b-#ee5a52&`
- **僵尸**：绿色

#### 苦力怕爆炸
```
[Team紫队]PlayerE 被苦力怕炸死了
```
- **PlayerE**：红色渐变 `&#ff6b6b-#ee5a52&`
- **苦力怕**：绿色

### 4. 特殊效果
#### 彩虹死亡
```
PlayerF 死了
```
- **PlayerF**：彩虹色 `&rainbow&`

#### 虚空死亡
```
[Team白队]PlayerG 掉出了这个世界
```
- **PlayerG**：紫色渐变 `&#667eea-#764ba2&`

## 🔧 配置选项

### Velocity端配置增强
```yaml
death_messages:
  enabled: true
  broadcast_to_all_servers: true
  include_source_server: false
  console_output: true
  
  # MineDown格式选项
  minedown:
    enabled: true
    use_gradients: true      # 使用渐变色
    use_rainbow: true        # 使用彩虹色
    enhance_team_names: true # 增强team名称显示

debug:
  enabled: true
  verbose_events: true
```

### Paper端配置
```yaml
death_messages:
  enabled: true
  cancel_original: true
  
  # MineDown格式选项
  minedown:
    enabled: true
    preserve_team_prefix: true  # 保留team前缀
    use_advanced_colors: true   # 使用高级颜色
```

## 📦 部署文件

### 更新的JAR文件
- **Velocity端**：`MessageTools-1.7-SNAPSHOT.jar` (包含MineDown库)
- **Paper端**：`messagetools-paper-1.0-SNAPSHOT.jar` (包含MineDown库)

### 依赖管理
- ✅ **MineDown库已内置**：两个插件都已经包含了MineDown库
- ✅ **重定位处理**：避免与其他插件冲突
- ✅ **版本兼容**：使用最新的MineDown-adventure版本

## 🎯 技术优势

### 1. 丰富的颜色支持
- ✅ **16种传统颜色**：支持所有Minecraft原版颜色
- ✅ **RGB颜色**：支持1600万种颜色
- ✅ **渐变色**：支持2-3色渐变效果
- ✅ **彩虹色**：支持动态彩虹效果
- ✅ **格式化**：支持粗体、斜体、下划线等

### 2. 智能颜色应用
- ✅ **死亡类型识别**：根据死亡类型应用不同颜色
- ✅ **Team前缀保留**：保持原有的团队显示
- ✅ **武器悬停效果**：保持原生的武器信息显示
- ✅ **自动回退**：解析失败时自动回退到纯文本

### 3. 性能优化
- ✅ **缓存机制**：避免重复解析相同格式
- ✅ **异常处理**：解析失败时优雅降级
- ✅ **内存管理**：自动清理临时对象

## ✅ 验证方法

### 1. 颜色效果测试
- [ ] **PvP死亡**：验证玩家名和武器的渐变色效果
- [ ] **环境死亡**：验证不同死亡原因的颜色区分
- [ ] **怪物攻击**：验证怪物类型的颜色标识
- [ ] **Team前缀**：确认team前缀正常显示

### 2. MineDown格式测试
- [ ] **RGB颜色**：测试 `&#ff0000&` 格式
- [ ] **渐变色**：测试 `&#ff0000-#0000ff&` 格式
- [ ] **彩虹色**：测试 `&rainbow&` 格式
- [ ] **格式化**：测试粗体、斜体等格式

### 3. 兼容性测试
- [ ] **原版消息**：确认原版死亡消息正常增强
- [ ] **自定义消息**：确认自定义死亡消息正常显示
- [ ] **错误处理**：确认格式错误时正常回退

## 🎉 关键改进

1. **策略转变**：从移除team前缀转为保留并增强显示效果
2. **MineDown集成**：完整支持MineDown的所有颜色格式
3. **智能增强**：根据死亡类型自动应用合适的颜色效果
4. **向后兼容**：保持与现有配置和功能的兼容性
5. **性能优化**：高效的颜色解析和应用机制

现在MessageTools支持完整的MineDown颜色格式，提供了丰富多彩的死亡消息显示效果，同时保留了team前缀的显示！🌈
