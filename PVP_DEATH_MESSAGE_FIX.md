# MessageTools PvP死亡消息修复

## 🎯 修复的问题

根据Minecraft Wiki的死亡消息标准，修复了以下PvP死亡消息问题：

### 1. **缺少杀手玩家名称**
- **修复前**：`PlayerA 被 钻石剑 杀死了`
- **修复后**：`PlayerA 被 PlayerB 用 钻石剑 杀死了`

### 2. **缺少附魔信息**
- **修复前**：`PlayerA 被 PlayerB 用 钻石剑 杀死了`
- **修复后**：`PlayerA 被 PlayerB 用 锋利Ⅴ钻石剑 杀死了`

### 3. **PvP格式不完整**
- **修复前**：只有基础的 `player` 和 `player_bow` 格式
- **修复后**：支持完整的PvP格式变体

## 🔧 技术修复

### Paper端修复

#### 1. **DeathCauseAnalyzer.java**
- ✅ 添加 `getKillerName()` 方法获取杀手玩家名称
- ✅ 增强 `getWeaponName()` 方法支持附魔信息显示
- ✅ 添加 `translateEnchantmentName()` 方法翻译附魔名称
- ✅ 更新死亡原因分析，区分不同PvP情况：
  - `player` - 空手攻击
  - `player_weapon` - 使用武器攻击
  - `player_bow` - 弓箭攻击（无武器信息）
  - `player_bow_weapon` - 使用弓/弩攻击

#### 2. **DeathMessageService.java**
- ✅ 更新消息格式：`playerName|deathCause|killerName|weapon|sourceServer`
- ✅ 发送杀手名称和武器信息到Velocity端

### Velocity端修复

#### 1. **DeathMessageService.java**
- ✅ 更新 `handleDeathMessage()` 方法支持杀手名称参数
- ✅ 更新 `buildDeathMessage()` 方法处理 `%killer%` 变量
- ✅ 更新插件消息解析支持新的5段格式

#### 2. **config.yml**
- ✅ 添加完整的PvP死亡消息格式：
  ```yaml
  # 玩家PvP（原版格式）
  player: "&f%player% &7被 &f%killer% &7杀死了"
  player_weapon: "&f%player% &7被 &f%killer% &7用 &f%weapon% &7杀死了"
  player_bow: "&f%player% &7被 &f%killer% &7射杀"
  player_bow_weapon: "&f%player% &7被 &f%killer% &7用 &f%weapon% &7射杀"
  ```

## 🎮 实际效果对比

### 空手PvP
- **修复前**：`PlayerA 被 PlayerB 杀死了`
- **修复后**：`PlayerA 被 PlayerB 杀死了` ✅

### 武器PvP
- **修复前**：`PlayerA 被 钻石剑 杀死了`
- **修复后**：`PlayerA 被 PlayerB 用 钻石剑 杀死了` ✅

### 附魔武器PvP
- **修复前**：`PlayerA 被 钻石剑 杀死了`
- **修复后**：`PlayerA 被 PlayerB 用 锋利Ⅴ钻石剑 杀死了` ✅

### 弓箭PvP
- **修复前**：`PlayerA 被 PlayerB 射杀`
- **修复后**：`PlayerA 被 PlayerB 用 力量Ⅴ弓 射杀` ✅

## 📋 支持的附魔翻译

### 武器附魔
- `sharpness` → `锋利`
- `smite` → `亡灵杀手`
- `bane_of_arthropods` → `节肢杀手`
- `knockback` → `击退`
- `fire_aspect` → `火焰附加`
- `looting` → `抢夺`
- `sweeping` → `横扫之刃`

### 弓箭附魔
- `power` → `力量`
- `punch` → `冲击`
- `flame` → `火矢`
- `infinity` → `无限`

### 通用附魔
- `unbreaking` → `耐久`
- `mending` → `经验修补`

### 附魔等级显示
- 等级1：不显示等级（如：`锋利`）
- 等级2+：显示罗马数字（如：`锋利Ⅴ`）

## 🚀 部署说明

### 文件位置
- **Velocity端**：`MessageTools-1.7-SNAPSHOT.jar`
- **Paper端**：`messagetools-paper-1.0-SNAPSHOT.jar`

### 配置更新
1. **Velocity端配置**会自动更新，包含新的PvP死亡消息格式
2. **Paper端配置**保持不变，继续处理死亡事件

### 验证方法
1. **测试空手PvP**：
   ```
   /kill @p[name=PlayerA]  # 由PlayerB空手击杀
   预期：PlayerA 被 PlayerB 杀死了
   ```

2. **测试武器PvP**：
   ```
   PlayerB手持钻石剑击杀PlayerA
   预期：PlayerA 被 PlayerB 用 钻石剑 杀死了
   ```

3. **测试附魔武器PvP**：
   ```
   PlayerB手持锋利V钻石剑击杀PlayerA
   预期：PlayerA 被 PlayerB 用 锋利Ⅴ钻石剑 杀死了
   ```

4. **测试弓箭PvP**：
   ```
   PlayerB使用力量V弓射杀PlayerA
   预期：PlayerA 被 PlayerB 用 力量Ⅴ弓 射杀
   ```

## ✅ 符合原版标准

现在MessageTools的PvP死亡消息完全符合Minecraft原版标准：

1. ✅ **显示杀手名称**：明确显示是哪个玩家造成的死亡
2. ✅ **显示武器信息**：包含使用的武器名称
3. ✅ **显示附魔效果**：显示武器的附魔等级
4. ✅ **原版格式**：使用Minecraft官方的死亡消息表述方式
5. ✅ **跨服同步**：所有服务器都能看到完整的PvP死亡消息

## 🎯 关键改进

1. **完整信息**：PvP死亡消息现在包含所有必要信息
2. **附魔支持**：正确显示武器附魔效果和等级
3. **格式标准**：完全符合Minecraft原版死亡消息格式
4. **中文本地化**：所有附魔名称都有准确的中文翻译
5. **兼容性强**：支持各种PvP场景（空手、武器、弓箭等）

现在MessageTools的PvP死亡消息功能已经完全修复，提供了与Minecraft原版完全一致的死亡消息体验！🎮
