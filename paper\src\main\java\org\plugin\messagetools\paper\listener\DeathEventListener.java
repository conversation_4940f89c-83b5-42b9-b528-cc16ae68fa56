package org.plugin.messagetools.paper.listener;

import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.plugin.messagetools.paper.service.DeathMessageService;

import java.util.logging.Logger;

/**
 * 死亡事件监听器
 * 负责监听玩家死亡事件
 */
public class DeathEventListener implements Listener {
    
    private final DeathMessageService deathMessageService;
    private final Logger logger;
    
    public DeathEventListener(DeathMessageService deathMessageService, Logger logger) {
        this.deathMessageService = deathMessageService;
        this.logger = logger;
    }
    
    /**
     * 监听玩家死亡事件
     * 使用HIGHEST优先级确保在其他插件处理之前获取原始信息
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerDeath(PlayerDeathEvent event) {
        try {
            // 检查死亡消息服务是否启用
            if (!deathMessageService.isEnabled()) {
                return;
            }
            
            // 处理死亡事件
            deathMessageService.handlePlayerDeath(event);
            
        } catch (Exception e) {
            logger.severe("处理玩家死亡事件时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
