# MessageTools 简化工作流程

## 🎯 返璞归真：简化的全栈工作流程

经过多次迭代和优化，MessageTools现在采用了更清晰、更高效的工作流程：

**玩家死亡信息输入拦截 → MessageTools-Paper解析颜色代码 → 将解析后的死亡信息发送到代理端 → 代理端MessageTools全服通告**

## 🔄 详细工作流程

### 第一步：玩家死亡信息输入拦截
```java
@EventHandler(priority = EventPriority.HIGHEST)
public void onPlayerDeathHighest(PlayerDeathEvent event) {
    // 拦截死亡事件
    deathMessageService.handlePlayerDeath(event);
}

@EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
public void onPlayerDeathMonitor(PlayerDeathEvent event) {
    // 取消原始死亡消息显示
    if (deathMessageService.shouldCancelOriginalMessage()) {
        event.deathMessage(null);
    }
}
```

### 第二步：MessageTools-Paper解析颜色代码
```java
public void handlePlayerDeath(PlayerDeathEvent event) {
    // 1. 防重复处理
    String deathId = player.getName() + "_" + System.currentTimeMillis();
    if (!processedDeaths.add(deathId)) return;
    
    // 2. 构建并解析死亡消息
    Component deathMessage = buildCompleteDeathMessage(event);
    
    // 3. 序列化为JSON
    String messageJson = GsonComponentSerializer.gson().serialize(deathMessage);
    
    // 4. 发送到代理端
    sendCompleteDeathMessageToProxy(player.getName(), messageJson);
}

private Component buildCompleteDeathMessage(PlayerDeathEvent event) {
    Component originalMessage = event.deathMessage();
    
    if (originalMessage != null) {
        // 直接使用原始死亡消息，应用MineDown颜色增强
        return enhanceOriginalMessageWithMineDown(originalMessage);
    }
    
    // 如果没有原始死亡消息，构建基础死亡消息
    return buildBasicDeathMessage(event);
}
```

### 第三步：将解析后的死亡信息发送到代理端
```java
private void sendCompleteDeathMessageToProxy(String playerName, String messageJson) {
    // 构建消息格式：playerName|messageJson|serverName
    String serverName = configManager.getString("server.name", "unknown");
    String messageData = playerName + "|" + messageJson + "|" + serverName;
    
    // 发送到Velocity代理
    ByteArrayDataOutput out = ByteStreams.newDataOutput();
    out.writeUTF(messageData);
    
    // 发送插件消息
    player.sendPluginMessage(plugin, DEATH_MESSAGE_CHANNEL, out.toByteArray());
}
```

### 第四步：代理端MessageTools全服通告
```java
public void handleCompleteDeathMessage(String playerName, String messageJson, String sourceServer) {
    try {
        // 反序列化死亡消息
        Component deathMessage = GsonComponentSerializer.gson().deserialize(messageJson);
        
        // 直接广播到所有服务器
        broadcastCompleteDeathMessage(deathMessage, sourceServer);
        
        // 控制台输出
        if (configManager.getBoolean("death_messages.console_output", true)) {
            String plainText = PlainTextComponentSerializer.plainText().serialize(deathMessage);
            logger.info("[死亡消息] {}", plainText);
        }
        
    } catch (Exception e) {
        logger.error("处理死亡消息时发生错误", e);
    }
}
```

## 🎨 MineDown颜色解析

### Paper端颜色增强
```java
private Component enhanceOriginalMessageWithMineDown(Component originalMessage) {
    // 将原始消息转换为纯文本
    String plainText = PlainTextComponentSerializer.plainText().serialize(originalMessage);
    
    // 应用基础的MineDown颜色增强
    String enhancedText = applyBasicMineDownColors(plainText);
    
    // 解析并返回增强后的消息
    return MineDownUtil.parse(enhancedText);
}

private String applyBasicMineDownColors(String text) {
    // 如果文本已经包含颜色代码，直接返回
    if (MineDownUtil.containsMineDownFormat(text)) {
        return text;
    }
    
    // 为普通死亡消息添加彩虹色效果
    return "&rainbow&" + text;
}
```

### 支持的MineDown格式
1. **传统颜色代码**：`&6Text`、`&cText`
2. **颜色名称**：`&gold&Text`、`&red&Text`
3. **RGB十六进制**：`&#ff00ff&Text`、`&#f0f&Text`
4. **渐变色**：`&#f0f-#fff&Text`
5. **彩虹色**：`&rainbow&Text`、`&rainbow:20&Text`
6. **格式化**：`**粗体**`、`##斜体##`、`__下划线__`

## 📊 消息流程图

```
玩家死亡
    ↓
Paper端拦截 (HIGHEST优先级)
    ↓
获取原始死亡消息
    ↓
应用MineDown颜色增强
    ↓
序列化为JSON
    ↓
发送到Velocity代理
    ↓
Velocity端接收
    ↓
反序列化JSON
    ↓
广播到所有服务器
    ↓
玩家看到彩色死亡消息
```

## 🔧 配置简化

### Velocity端配置
```yaml
# MessageTools 配置文件 (Velocity端)
death_messages:
  enabled: true
  broadcast_to_all_servers: true
  include_source_server: false  # 避免重复发送
  console_output: true

debug:
  enabled: true      # 查看处理日志
  verbose_events: true
```

### Paper端配置
```yaml
# MessageTools-Paper 配置文件
death_messages:
  enabled: true
  cancel_original: true  # 取消原始死亡消息

debug:
  enabled: true
  verbose_events: true
```

## 🎯 关键优势

### 1. 流程简化
- ✅ **单一职责**：Paper端负责解析，Velocity端负责广播
- ✅ **清晰流程**：拦截→解析→发送→广播
- ✅ **减少复杂性**：移除了复杂的死亡原因分析和映射

### 2. 颜色支持增强
- ✅ **MineDown集成**：完整支持所有MineDown颜色格式
- ✅ **Team前缀保留**：保持原有的团队显示
- ✅ **智能增强**：自动检测并增强颜色效果
- ✅ **向后兼容**：支持原有的颜色代码

### 3. 性能优化
- ✅ **防重复机制**：避免同一死亡事件被多次处理
- ✅ **高效序列化**：使用Adventure的原生JSON序列化
- ✅ **内存管理**：自动清理处理记录

### 4. 调试友好
- ✅ **详细日志**：提供完整的处理流程日志
- ✅ **错误处理**：优雅的异常处理和回退机制
- ✅ **配置灵活**：可以独立控制各个功能模块

## 📋 验证清单

### 1. 基础功能测试
- [ ] **死亡事件拦截**：确认死亡事件被正确拦截
- [ ] **原始消息取消**：确认原始死亡消息被取消
- [ ] **消息发送**：确认消息正确发送到Velocity
- [ ] **跨服广播**：确认消息在所有服务器显示

### 2. 颜色效果测试
- [ ] **MineDown解析**：确认MineDown格式被正确解析
- [ ] **颜色显示**：确认颜色效果正常显示
- [ ] **Team前缀**：确认team前缀正常显示
- [ ] **武器悬停**：确认武器悬停效果正常

### 3. 性能测试
- [ ] **防重复**：确认重复死亡事件被正确跳过
- [ ] **内存使用**：确认内存使用正常
- [ ] **处理速度**：确认消息处理速度正常

## 🎉 总结

通过这次返璞归真的重构，MessageTools现在拥有了：

1. **更清晰的架构**：简单的四步工作流程
2. **更强大的颜色支持**：完整的MineDown格式支持
3. **更好的性能**：优化的处理流程和防重复机制
4. **更好的维护性**：简化的代码结构和清晰的职责分工

MessageTools现在真正实现了"拦截→解析→发送→广播"的简化流程，同时保持了强大的功能和出色的性能！🚀
