<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="org.plugin.messagetools.util.ColorUtilTest" time="0.183" tests="7" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="D:\MinecraftPlugins\MessageTools\target\test-classes;D:\MinecraftPlugins\MessageTools\target\classes;C:\Users\<USER>\.m2\repository\com\velocitypowered\velocity-api\3.4.0-SNAPSHOT\velocity-api-3.4.0-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.10.1\gson-2.10.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\25.1-jre\guava-25.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.1.3\error_prone_annotations-2.1.3.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.1\j2objc-annotations-1.1.jar;C:\Users\<USER>\.m2\repository\org\codehaus\mojo\animal-sniffer-annotations\1.14\animal-sniffer-annotations-1.14.jar;C:\Users\<USER>\.m2\repository\com\moandjiezana\toml\toml4j\0.7.2\toml4j-0.7.2.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-api\4.21.0\adventure-api-4.21.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-key\4.21.0\adventure-key-4.21.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\examination-api\1.3.0\examination-api-1.3.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\examination-string\1.3.0\examination-string-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\26.0.2\annotations-26.0.2.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-gson\4.21.0\adventure-text-serializer-gson-4.21.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-json\4.21.0\adventure-text-serializer-json-4.21.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\option\1.1.0\option-1.1.0.jar;C:\Users\<USER>\.m2\repository\com\google\auto\service\auto-service-annotations\1.1.1\auto-service-annotations-1.1.1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-commons\4.21.0\adventure-text-serializer-commons-4.21.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-legacy\4.21.0\adventure-text-serializer-legacy-4.21.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-plain\4.21.0\adventure-text-serializer-plain-4.21.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-logger-slf4j\4.21.0\adventure-text-logger-slf4j-4.21.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-ansi\4.21.0\adventure-text-serializer-ansi-4.21.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\ansi\1.1.1\ansi-1.1.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.17\slf4j-api-2.0.17.jar;C:\Users\<USER>\.m2\repository\com\google\inject\guice\6.0.0\guice-6.0.0.jar;C:\Users\<USER>\.m2\repository\javax\inject\javax.inject\1\javax.inject-1.jar;C:\Users\<USER>\.m2\repository\jakarta\inject\jakarta.inject-api\2.0.1\jakarta.inject-api-2.0.1.jar;C:\Users\<USER>\.m2\repository\aopalliance\aopalliance\1.0\aopalliance-1.0.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.42.0\checker-qual-3.42.0.jar;C:\Users\<USER>\.m2\repository\com\velocitypowered\velocity-brigadier\1.0.0-SNAPSHOT\velocity-brigadier-1.0.0-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\org\spongepowered\configurate-hocon\4.1.2\configurate-hocon-4.1.2.jar;C:\Users\<USER>\.m2\repository\org\spongepowered\configurate-core\4.1.2\configurate-core-4.1.2.jar;C:\Users\<USER>\.m2\repository\io\leangen\geantyref\geantyref\1.3.11\geantyref-1.3.11.jar;C:\Users\<USER>\.m2\repository\com\typesafe\config\1.4.1\config-1.4.1.jar;C:\Users\<USER>\.m2\repository\org\spongepowered\configurate-yaml\4.1.2\configurate-yaml-4.1.2.jar;C:\Users\<USER>\.m2\repository\org\spongepowered\configurate-gson\4.1.2\configurate-gson-4.1.2.jar;C:\Users\<USER>\.m2\repository\com\github\ben-manes\caffeine\caffeine\3.1.8\caffeine-3.1.8.jar;C:\Users\<USER>\.m2\repository\org\jspecify\jspecify\0.3.0\jspecify-0.3.0.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\2.0\snakeyaml-2.0.jar;C:\Users\<USER>\.m2\repository\net\william278\papiproxybridge\1.6.1\papiproxybridge-1.6.1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-minimessage\4.14.0\adventure-text-minimessage-4.14.0.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.9.2\junit-jupiter-5.9.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.9.2\junit-jupiter-api-5.9.2.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.9.2\junit-platform-commons-1.9.2.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.9.2\junit-jupiter-params-5.9.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.9.2\junit-jupiter-engine-5.9.2.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.9.2\junit-platform-engine-1.9.2.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="C:\Program Files\Java\jdk-17\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire2169329017427110516\surefirebooter-20250626152721265_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire2169329017427110516 2025-06-26T15-27-20_361-jvmRun1 surefire-20250626152721265_1tmp surefire_0-20250626152721265_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="D:\MinecraftPlugins\MessageTools\target\test-classes;D:\MinecraftPlugins\MessageTools\target\classes;C:\Users\<USER>\.m2\repository\com\velocitypowered\velocity-api\3.4.0-SNAPSHOT\velocity-api-3.4.0-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.10.1\gson-2.10.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\25.1-jre\guava-25.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.1.3\error_prone_annotations-2.1.3.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.1\j2objc-annotations-1.1.jar;C:\Users\<USER>\.m2\repository\org\codehaus\mojo\animal-sniffer-annotations\1.14\animal-sniffer-annotations-1.14.jar;C:\Users\<USER>\.m2\repository\com\moandjiezana\toml\toml4j\0.7.2\toml4j-0.7.2.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-api\4.21.0\adventure-api-4.21.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-key\4.21.0\adventure-key-4.21.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\examination-api\1.3.0\examination-api-1.3.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\examination-string\1.3.0\examination-string-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\26.0.2\annotations-26.0.2.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-gson\4.21.0\adventure-text-serializer-gson-4.21.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-json\4.21.0\adventure-text-serializer-json-4.21.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\option\1.1.0\option-1.1.0.jar;C:\Users\<USER>\.m2\repository\com\google\auto\service\auto-service-annotations\1.1.1\auto-service-annotations-1.1.1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-commons\4.21.0\adventure-text-serializer-commons-4.21.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-legacy\4.21.0\adventure-text-serializer-legacy-4.21.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-plain\4.21.0\adventure-text-serializer-plain-4.21.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-logger-slf4j\4.21.0\adventure-text-logger-slf4j-4.21.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-ansi\4.21.0\adventure-text-serializer-ansi-4.21.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\ansi\1.1.1\ansi-1.1.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.17\slf4j-api-2.0.17.jar;C:\Users\<USER>\.m2\repository\com\google\inject\guice\6.0.0\guice-6.0.0.jar;C:\Users\<USER>\.m2\repository\javax\inject\javax.inject\1\javax.inject-1.jar;C:\Users\<USER>\.m2\repository\jakarta\inject\jakarta.inject-api\2.0.1\jakarta.inject-api-2.0.1.jar;C:\Users\<USER>\.m2\repository\aopalliance\aopalliance\1.0\aopalliance-1.0.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.42.0\checker-qual-3.42.0.jar;C:\Users\<USER>\.m2\repository\com\velocitypowered\velocity-brigadier\1.0.0-SNAPSHOT\velocity-brigadier-1.0.0-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\org\spongepowered\configurate-hocon\4.1.2\configurate-hocon-4.1.2.jar;C:\Users\<USER>\.m2\repository\org\spongepowered\configurate-core\4.1.2\configurate-core-4.1.2.jar;C:\Users\<USER>\.m2\repository\io\leangen\geantyref\geantyref\1.3.11\geantyref-1.3.11.jar;C:\Users\<USER>\.m2\repository\com\typesafe\config\1.4.1\config-1.4.1.jar;C:\Users\<USER>\.m2\repository\org\spongepowered\configurate-yaml\4.1.2\configurate-yaml-4.1.2.jar;C:\Users\<USER>\.m2\repository\org\spongepowered\configurate-gson\4.1.2\configurate-gson-4.1.2.jar;C:\Users\<USER>\.m2\repository\com\github\ben-manes\caffeine\caffeine\3.1.8\caffeine-3.1.8.jar;C:\Users\<USER>\.m2\repository\org\jspecify\jspecify\0.3.0\jspecify-0.3.0.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\2.0\snakeyaml-2.0.jar;C:\Users\<USER>\.m2\repository\net\william278\papiproxybridge\1.6.1\papiproxybridge-1.6.1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-minimessage\4.14.0\adventure-text-minimessage-4.14.0.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.9.2\junit-jupiter-5.9.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.9.2\junit-jupiter-api-5.9.2.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.9.2\junit-platform-commons-1.9.2.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.9.2\junit-jupiter-params-5.9.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.9.2\junit-jupiter-engine-5.9.2.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.9.2\junit-platform-engine-1.9.2.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Java\jdk-17"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="D:\MinecraftPlugins\MessageTools"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire2169329017427110516\surefirebooter-20250626152721265_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="17.0.7+8-LTS-224"/>
    <property name="user.name" value="user"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="17.0.7"/>
    <property name="user.dir" value="D:\MinecraftPlugins\MessageTools"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="native.encoding" value="GBK"/>
    <property name="java.library.path" value="C:\Program Files\Java\jdk-17\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\apache-maven-3.9.9\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;D:\Program Files\IntelliJ IDEA Community Edition 2023.3.1\bin;;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="17.0.7+8-LTS-224"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="testGetColorFromCode" classname="org.plugin.messagetools.util.ColorUtilTest" time="0.109"/>
  <testcase name="testIsValidColorCode" classname="org.plugin.messagetools.util.ColorUtilTest" time="0.003"/>
  <testcase name="testComponentToString" classname="org.plugin.messagetools.util.ColorUtilTest" time="0.014"/>
  <testcase name="testHasColorCodes" classname="org.plugin.messagetools.util.ColorUtilTest" time="0.002"/>
  <testcase name="testStripColorCodes" classname="org.plugin.messagetools.util.ColorUtilTest" time="0.002"/>
  <testcase name="testCreateColoredText" classname="org.plugin.messagetools.util.ColorUtilTest" time="0.001"/>
  <testcase name="testParseColorCodes" classname="org.plugin.messagetools.util.ColorUtilTest" time="0.007"/>
</testsuite>