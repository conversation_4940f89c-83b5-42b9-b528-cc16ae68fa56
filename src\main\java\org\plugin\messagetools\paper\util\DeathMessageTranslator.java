package org.plugin.messagetools.paper.util;

import org.bukkit.Material;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.inventory.ItemStack;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * 死亡消息翻译器
 * 将英语死亡消息翻译为中文，并保持MineDown颜色格式
 */
public class DeathMessageTranslator {
    
    // 死亡消息翻译映射
    private static final Map<String, String> DEATH_MESSAGE_TRANSLATIONS = new HashMap<>();
    
    // 武器名称翻译映射
    private static final Map<String, String> WEAPON_TRANSLATIONS = new HashMap<>();
    
    // 实体名称翻译映射
    private static final Map<String, String> ENTITY_TRANSLATIONS = new HashMap<>();
    
    static {
        initializeDeathMessageTranslations();
        initializeWeaponTranslations();
        initializeEntityTranslations();
    }
    
    /**
     * 初始化死亡消息翻译
     */
    private static void initializeDeathMessageTranslations() {
        // PvP死亡消息
        DEATH_MESSAGE_TRANSLATIONS.put("{player} was slain by {killer}", "{player} 被 {killer} 杀死了");
        DEATH_MESSAGE_TRANSLATIONS.put("{player} was slain by {killer} using {weapon}", "{player} 被 {killer} 用 {weapon} 杀死了");
        DEATH_MESSAGE_TRANSLATIONS.put("{player} was shot by {killer}", "{player} 被 {killer} 射杀");
        DEATH_MESSAGE_TRANSLATIONS.put("{player} was shot by {killer} using {weapon}", "{player} 被 {killer} 用 {weapon} 射杀");
        DEATH_MESSAGE_TRANSLATIONS.put("{player} was killed by {killer}", "{player} 被 {killer} 杀死了");
        
        // 环境死亡消息
        DEATH_MESSAGE_TRANSLATIONS.put("{player} fell from a high place", "{player} 从高处摔了下来");
        DEATH_MESSAGE_TRANSLATIONS.put("{player} hit the ground too hard", "{player} 落地过猛");
        DEATH_MESSAGE_TRANSLATIONS.put("{player} fell out of the world", "{player} 掉出了这个世界");
        DEATH_MESSAGE_TRANSLATIONS.put("{player} drowned", "{player} 淹死了");
        DEATH_MESSAGE_TRANSLATIONS.put("{player} tried to swim in lava", "{player} 试图在熔岩里游泳");
        DEATH_MESSAGE_TRANSLATIONS.put("{player} burned to death", "{player} 被烧死了");
        DEATH_MESSAGE_TRANSLATIONS.put("{player} went up in flames", "{player} 被火焰吞噬");
        DEATH_MESSAGE_TRANSLATIONS.put("{player} suffocated in a wall", "{player} 在墙里窒息而亡");
        DEATH_MESSAGE_TRANSLATIONS.put("{player} starved to death", "{player} 饿死了");
        DEATH_MESSAGE_TRANSLATIONS.put("{player} was pricked to death", "{player} 被扎死了");
        DEATH_MESSAGE_TRANSLATIONS.put("{player} walked into a cactus whilst trying to escape {killer}", "{player} 在试图逃离 {killer} 时撞上了仙人掌");
        DEATH_MESSAGE_TRANSLATIONS.put("{player} was struck by lightning", "{player} 被闪电击中");
        DEATH_MESSAGE_TRANSLATIONS.put("{player} blew up", "{player} 爆炸了");
        DEATH_MESSAGE_TRANSLATIONS.put("{player} was blown up by {killer}", "{player} 被 {killer} 炸死了");
        DEATH_MESSAGE_TRANSLATIONS.put("{player} was blown up by {killer} using {weapon}", "{player} 被 {killer} 用 {weapon} 炸死了");
        
        // 怪物攻击死亡消息
        DEATH_MESSAGE_TRANSLATIONS.put("{player} was slain by {mob}", "{player} 被 {mob} 杀死了");
        DEATH_MESSAGE_TRANSLATIONS.put("{player} was shot by {mob}", "{player} 被 {mob} 射死了");
        DEATH_MESSAGE_TRANSLATIONS.put("{player} was killed by {mob}", "{player} 被 {mob} 杀死了");
        
        // 其他死亡消息
        DEATH_MESSAGE_TRANSLATIONS.put("{player} died", "{player} 死了");
        DEATH_MESSAGE_TRANSLATIONS.put("{player} withered away", "{player} 凋零了");
        DEATH_MESSAGE_TRANSLATIONS.put("{player} was poisoned", "{player} 中毒身亡");
        DEATH_MESSAGE_TRANSLATIONS.put("{player} was magic", "{player} 被魔法杀死了");
    }
    
    /**
     * 初始化武器翻译
     */
    private static void initializeWeaponTranslations() {
        // 剑类
        WEAPON_TRANSLATIONS.put("wooden_sword", "木剑");
        WEAPON_TRANSLATIONS.put("stone_sword", "石剑");
        WEAPON_TRANSLATIONS.put("iron_sword", "铁剑");
        WEAPON_TRANSLATIONS.put("golden_sword", "金剑");
        WEAPON_TRANSLATIONS.put("diamond_sword", "钻石剑");
        WEAPON_TRANSLATIONS.put("netherite_sword", "下界合金剑");
        
        // 斧类
        WEAPON_TRANSLATIONS.put("wooden_axe", "木斧");
        WEAPON_TRANSLATIONS.put("stone_axe", "石斧");
        WEAPON_TRANSLATIONS.put("iron_axe", "铁斧");
        WEAPON_TRANSLATIONS.put("golden_axe", "金斧");
        WEAPON_TRANSLATIONS.put("diamond_axe", "钻石斧");
        WEAPON_TRANSLATIONS.put("netherite_axe", "下界合金斧");
        
        // 远程武器
        WEAPON_TRANSLATIONS.put("bow", "弓");
        WEAPON_TRANSLATIONS.put("crossbow", "弩");
        WEAPON_TRANSLATIONS.put("trident", "三叉戟");
        
        // 工具类
        WEAPON_TRANSLATIONS.put("wooden_pickaxe", "木镐");
        WEAPON_TRANSLATIONS.put("stone_pickaxe", "石镐");
        WEAPON_TRANSLATIONS.put("iron_pickaxe", "铁镐");
        WEAPON_TRANSLATIONS.put("golden_pickaxe", "金镐");
        WEAPON_TRANSLATIONS.put("diamond_pickaxe", "钻石镐");
        WEAPON_TRANSLATIONS.put("netherite_pickaxe", "下界合金镐");
        
        // 其他武器
        WEAPON_TRANSLATIONS.put("stick", "木棍");
        WEAPON_TRANSLATIONS.put("stone", "石头");
        WEAPON_TRANSLATIONS.put("cobblestone", "圆石");
    }
    
    /**
     * 初始化实体翻译
     */
    private static void initializeEntityTranslations() {
        ENTITY_TRANSLATIONS.put("zombie", "僵尸");
        ENTITY_TRANSLATIONS.put("skeleton", "骷髅");
        ENTITY_TRANSLATIONS.put("creeper", "苦力怕");
        ENTITY_TRANSLATIONS.put("spider", "蜘蛛");
        ENTITY_TRANSLATIONS.put("enderman", "末影人");
        ENTITY_TRANSLATIONS.put("witch", "女巫");
        ENTITY_TRANSLATIONS.put("blaze", "烈焰人");
        ENTITY_TRANSLATIONS.put("ghast", "恶魂");
        ENTITY_TRANSLATIONS.put("slime", "史莱姆");
        ENTITY_TRANSLATIONS.put("magma_cube", "岩浆怪");
        ENTITY_TRANSLATIONS.put("silverfish", "蠹虫");
        ENTITY_TRANSLATIONS.put("cave_spider", "洞穴蜘蛛");
        ENTITY_TRANSLATIONS.put("endermite", "末影螨");
        ENTITY_TRANSLATIONS.put("guardian", "守卫者");
        ENTITY_TRANSLATIONS.put("elder_guardian", "远古守卫者");
        ENTITY_TRANSLATIONS.put("shulker", "潜影贝");
        ENTITY_TRANSLATIONS.put("phantom", "幻翼");
        ENTITY_TRANSLATIONS.put("drowned", "溺尸");
        ENTITY_TRANSLATIONS.put("husk", "尸壳");
        ENTITY_TRANSLATIONS.put("stray", "流浪者");
        ENTITY_TRANSLATIONS.put("wither_skeleton", "凋灵骷髅");
        ENTITY_TRANSLATIONS.put("zombified_piglin", "僵尸猪灵");
        ENTITY_TRANSLATIONS.put("piglin", "猪灵");
        ENTITY_TRANSLATIONS.put("piglin_brute", "猪灵蛮兵");
        ENTITY_TRANSLATIONS.put("hoglin", "疣猪兽");
        ENTITY_TRANSLATIONS.put("zoglin", "僵尸疣猪兽");
        ENTITY_TRANSLATIONS.put("strider", "炽足兽");
        ENTITY_TRANSLATIONS.put("warden", "监守者");
    }
    
    /**
     * 翻译死亡消息
     *
     * @param originalMessage 原始死亡消息
     * @param event 死亡事件
     * @param configManager 配置管理器
     * @return 翻译后的死亡消息
     */
    public static Component translateDeathMessage(Component originalMessage, PlayerDeathEvent event, org.plugin.messagetools.paper.config.ConfigManager configManager) {
        // 检查是否启用翻译
        if (!configManager.getBoolean("death_messages.translation.enabled", true)) {
            // 如果未启用翻译，直接应用MineDown颜色增强
            if (originalMessage != null) {
                String plainText = PlainTextComponentSerializer.plainText().serialize(originalMessage);
                return MineDownUtil.parse("&rainbow&" + plainText);
            } else {
                return buildBasicDeathMessage(event);
            }
        }

        // 检查目标语言
        String targetLanguage = configManager.getString("death_messages.translation.target_language", "zh_cn");
        if (!"zh_cn".equals(targetLanguage)) {
            // 如果不是中文，直接应用颜色增强
            if (originalMessage != null) {
                String plainText = PlainTextComponentSerializer.plainText().serialize(originalMessage);
                return MineDownUtil.parse("&rainbow&" + plainText);
            } else {
                return buildBasicDeathMessage(event);
            }
        }

        if (originalMessage == null) {
            return buildChineseDeathMessage(event);
        }

        // 将Component转换为纯文本
        String plainText = PlainTextComponentSerializer.plainText().serialize(originalMessage);

        // 翻译死亡消息
        String translatedText = translateDeathMessageText(plainText, event, configManager);

        // 应用MineDown颜色格式
        return MineDownUtil.parse(translatedText);
    }

    /**
     * 构建基础死亡消息（未启用翻译时）
     */
    private static Component buildBasicDeathMessage(PlayerDeathEvent event) {
        Player player = event.getEntity();
        String playerName = player.displayName() != null ?
            PlainTextComponentSerializer.plainText().serialize(player.displayName()) :
            player.getName();

        return MineDownUtil.parse("&rainbow&" + playerName + " died");
    }
    
    /**
     * 翻译死亡消息文本
     */
    private static String translateDeathMessageText(String originalText, PlayerDeathEvent event, org.plugin.messagetools.paper.config.ConfigManager configManager) {
        Player player = event.getEntity();
        String playerName = player.displayName() != null ? 
            PlainTextComponentSerializer.plainText().serialize(player.displayName()) : 
            player.getName();
        
        // 尝试匹配和翻译各种死亡消息模式
        String translatedText = originalText;
        
        // 替换玩家名称为占位符
        translatedText = translatedText.replace(player.getName(), "{player}");
        
        // 处理PvP死亡消息
        if (player.getKiller() != null) {
            Player killer = player.getKiller();
            String killerName = killer.displayName() != null ? 
                PlainTextComponentSerializer.plainText().serialize(killer.displayName()) : 
                killer.getName();
            
            translatedText = translatedText.replace(killer.getName(), "{killer}");
            
            // 处理武器
            ItemStack weapon = killer.getInventory().getItemInMainHand();
            if (weapon != null && weapon.getType() != Material.AIR) {
                String weaponName = getWeaponDisplayName(weapon);
                translatedText = translatedText.replace(weaponName, "{weapon}");
            }
        }
        
        // 处理怪物攻击
        EntityDamageEvent damageEvent = player.getLastDamageCause();
        if (damageEvent instanceof org.bukkit.event.entity.EntityDamageByEntityEvent) {
            org.bukkit.event.entity.EntityDamageByEntityEvent entityEvent = 
                (org.bukkit.event.entity.EntityDamageByEntityEvent) damageEvent;
            Entity damager = entityEvent.getDamager();
            
            if (!(damager instanceof Player)) {
                String mobName = damager.getType().name().toLowerCase();
                String translatedMobName = ENTITY_TRANSLATIONS.getOrDefault(mobName, mobName);
                translatedText = translatedText.replace(mobName, "{mob}");
            }
        }
        
        // 查找匹配的翻译模式
        for (Map.Entry<String, String> entry : DEATH_MESSAGE_TRANSLATIONS.entrySet()) {
            String pattern = entry.getKey();
            String translation = entry.getValue();
            
            if (matchesPattern(translatedText, pattern)) {
                return applyTranslation(translation, playerName, event);
            }
        }
        
        // 如果没有找到匹配的翻译，返回默认翻译
        return "&#ff6b6b-#ee5a52&" + playerName + " &7死了";
    }
    
    /**
     * 检查文本是否匹配模式
     */
    private static boolean matchesPattern(String text, String pattern) {
        // 简单的模式匹配，可以根据需要扩展
        String regex = pattern.replace("{player}", ".*")
                             .replace("{killer}", ".*")
                             .replace("{weapon}", ".*")
                             .replace("{mob}", ".*");
        return text.matches(regex);
    }
    
    /**
     * 应用翻译
     */
    private static String applyTranslation(String translation, String playerName, PlayerDeathEvent event) {
        String result = translation.replace("{player}", "&#ff6b6b-#ee5a52&" + playerName);
        
        // 处理击杀者
        if (event.getEntity().getKiller() != null) {
            Player killer = event.getEntity().getKiller();
            String killerName = killer.displayName() != null ? 
                PlainTextComponentSerializer.plainText().serialize(killer.displayName()) : 
                killer.getName();
            result = result.replace("{killer}", "&#4ecdc4-#44a08d&" + killerName);
            
            // 处理武器
            ItemStack weapon = killer.getInventory().getItemInMainHand();
            if (weapon != null && weapon.getType() != Material.AIR) {
                String weaponName = getWeaponDisplayName(weapon);
                String translatedWeaponName = translateWeaponName(weaponName);
                result = result.replace("{weapon}", "&#ffd93d-#6bcf7f&[" + translatedWeaponName + "]");
            }
        }
        
        // 处理怪物
        EntityDamageEvent damageEvent = event.getEntity().getLastDamageCause();
        if (damageEvent instanceof org.bukkit.event.entity.EntityDamageByEntityEvent) {
            org.bukkit.event.entity.EntityDamageByEntityEvent entityEvent = 
                (org.bukkit.event.entity.EntityDamageByEntityEvent) damageEvent;
            Entity damager = entityEvent.getDamager();
            
            if (!(damager instanceof Player)) {
                String mobName = damager.getType().name().toLowerCase();
                String translatedMobName = ENTITY_TRANSLATIONS.getOrDefault(mobName, mobName);
                result = result.replace("{mob}", "&#ff5722&" + translatedMobName);
            }
        }
        
        return result;
    }
    
    /**
     * 获取武器显示名称
     */
    private static String getWeaponDisplayName(ItemStack weapon) {
        if (weapon.hasItemMeta() && weapon.getItemMeta().hasDisplayName()) {
            return PlainTextComponentSerializer.plainText().serialize(weapon.getItemMeta().displayName());
        }
        return weapon.getType().name().toLowerCase();
    }
    
    /**
     * 翻译武器名称
     */
    private static String translateWeaponName(String weaponName) {
        return WEAPON_TRANSLATIONS.getOrDefault(weaponName.toLowerCase(), weaponName);
    }
    
    /**
     * 构建中文死亡消息（当没有原始消息时）
     */
    private static Component buildChineseDeathMessage(PlayerDeathEvent event) {
        Player player = event.getEntity();
        String playerName = player.displayName() != null ? 
            PlainTextComponentSerializer.plainText().serialize(player.displayName()) : 
            player.getName();
        
        EntityDamageEvent damageEvent = player.getLastDamageCause();
        
        if (damageEvent == null) {
            return MineDownUtil.parse("&#ff6b6b-#ee5a52&" + playerName + " &7死了");
        }
        
        // 根据伤害类型构建中文死亡消息
        if (damageEvent instanceof org.bukkit.event.entity.EntityDamageByEntityEvent) {
            org.bukkit.event.entity.EntityDamageByEntityEvent entityEvent = 
                (org.bukkit.event.entity.EntityDamageByEntityEvent) damageEvent;
            Entity damager = entityEvent.getDamager();
            
            if (damager instanceof Player) {
                Player killer = (Player) damager;
                String killerName = killer.displayName() != null ? 
                    PlainTextComponentSerializer.plainText().serialize(killer.displayName()) : 
                    killer.getName();
                
                ItemStack weapon = killer.getInventory().getItemInMainHand();
                if (weapon != null && weapon.getType() != Material.AIR) {
                    String weaponName = getWeaponDisplayName(weapon);
                    String translatedWeaponName = translateWeaponName(weaponName);
                    return MineDownUtil.parse("&#ff6b6b-#ee5a52&" + playerName + 
                                            " &7被 &#4ecdc4-#44a08d&" + killerName + 
                                            " &7用 &#ffd93d-#6bcf7f&[" + translatedWeaponName + "] &7杀死了");
                } else {
                    return MineDownUtil.parse("&#ff6b6b-#ee5a52&" + playerName + 
                                            " &7被 &#4ecdc4-#44a08d&" + killerName + 
                                            " &7杀死了");
                }
            } else {
                String mobName = damager.getType().name().toLowerCase();
                String translatedMobName = ENTITY_TRANSLATIONS.getOrDefault(mobName, mobName);
                return MineDownUtil.parse("&#ff6b6b-#ee5a52&" + playerName + 
                                        " &7被 &#ff5722&" + translatedMobName + " &7杀死了");
            }
        }
        
        // 环境死亡
        switch (damageEvent.getCause()) {
            case FALL:
                return MineDownUtil.parse("&#ff9a9e-#fecfef&" + playerName + " &7落地过猛");
            case DROWNING:
                return MineDownUtil.parse("&#a8edea-#fed6e3&" + playerName + " &9淹死了");
            case LAVA:
                return MineDownUtil.parse("&#ff9a56-#ff6b35&" + playerName + " &6试图在熔岩里游泳");
            case FIRE:
            case FIRE_TICK:
                return MineDownUtil.parse("&#ff9a56-#ff6b35&" + playerName + " &6被烧死了");
            case SUFFOCATION:
                return MineDownUtil.parse("&#9e9e9e-#616161&" + playerName + " &7在墙里窒息而亡");
            case VOID:
                return MineDownUtil.parse("&#667eea-#764ba2&" + playerName + " &5掉出了这个世界");
            case STARVATION:
                return MineDownUtil.parse("&#8bc34a-#4caf50&" + playerName + " &a饿死了");
            case POISON:
                return MineDownUtil.parse("&#9c27b0-#673ab7&" + playerName + " &5中毒身亡");
            case WITHER:
                return MineDownUtil.parse("&#424242-#212121&" + playerName + " &8凋零了");
            case LIGHTNING:
                return MineDownUtil.parse("&#ffeb3b-#ffc107&" + playerName + " &e被闪电击中");
            case ENTITY_EXPLOSION:
            case BLOCK_EXPLOSION:
                return MineDownUtil.parse("&#ffd93d-#6bcf7f&" + playerName + " &e爆炸了");
            default:
                return MineDownUtil.parse("&rainbow&" + playerName + " &7死了");
        }
    }
}
