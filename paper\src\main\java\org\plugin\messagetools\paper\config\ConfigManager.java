package org.plugin.messagetools.paper.config;

import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.List;
import java.util.Set;

/**
 * 配置管理器
 * 负责加载和管理插件配置
 */
public class ConfigManager {
    
    private final JavaPlugin plugin;
    private FileConfiguration config;
    
    public ConfigManager(JavaPlugin plugin) {
        this.plugin = plugin;
    }
    
    /**
     * 加载配置文件
     */
    public void loadConfig() {
        // 保存默认配置文件
        plugin.saveDefaultConfig();
        
        // 重新加载配置
        plugin.reloadConfig();
        config = plugin.getConfig();
        
        plugin.getLogger().info("配置文件已加载");
    }
    
    /**
     * 保存配置文件
     */
    public void saveConfig() {
        plugin.saveConfig();
        plugin.getLogger().info("配置文件已保存");
    }
    
    /**
     * 重新加载配置文件
     */
    public void reloadConfig() {
        plugin.reloadConfig();
        config = plugin.getConfig();
        plugin.getLogger().info("配置文件已重新加载");
    }
    
    /**
     * 获取字符串配置值
     */
    public String getString(String path, String defaultValue) {
        return config.getString(path, defaultValue);
    }
    
    /**
     * 获取布尔配置值
     */
    public boolean getBoolean(String path, boolean defaultValue) {
        return config.getBoolean(path, defaultValue);
    }
    
    /**
     * 获取整数配置值
     */
    public int getInt(String path, int defaultValue) {
        return config.getInt(path, defaultValue);
    }
    
    /**
     * 获取长整数配置值
     */
    public long getLong(String path, long defaultValue) {
        return config.getLong(path, defaultValue);
    }
    
    /**
     * 获取双精度配置值
     */
    public double getDouble(String path, double defaultValue) {
        return config.getDouble(path, defaultValue);
    }
    
    /**
     * 获取字符串列表配置值
     */
    public List<String> getStringList(String path) {
        return config.getStringList(path);
    }
    
    /**
     * 获取配置节
     */
    public ConfigurationSection getConfigurationSection(String path) {
        return config.getConfigurationSection(path);
    }
    
    /**
     * 检查配置路径是否存在
     */
    public boolean contains(String path) {
        return config.contains(path);
    }
    
    /**
     * 获取配置节的所有键
     */
    public Set<String> getKeys(String path, boolean deep) {
        ConfigurationSection section = getConfigurationSection(path);
        return section != null ? section.getKeys(deep) : Set.of();
    }
    
    /**
     * 设置配置值
     */
    public void set(String path, Object value) {
        config.set(path, value);
    }
    
    /**
     * 获取原始配置对象
     */
    public FileConfiguration getConfig() {
        return config;
    }
}
