package org.plugin.messagetools.paper.util;

import de.themoep.minedown.adventure.MineDown;
import net.kyori.adventure.text.Component;

/**
 * MineDown工具类
 * 提供MineDown格式的文本处理功能
 */
public class MineDownUtil {

    /**
     * 将MineDown格式的文本转换为Adventure Component
     * 
     * @param text MineDown格式的文本
     * @return Adventure Component
     */
    public static Component parse(String text) {
        if (text == null || text.isEmpty()) {
            return Component.empty();
        }
        
        try {
            return new MineDown(text).toComponent();
        } catch (Exception e) {
            // 如果解析失败，返回纯文本
            return Component.text(text);
        }
    }
    
    /**
     * 将MineDown格式的文本转换为Adventure Component，支持占位符替换
     * 
     * @param text MineDown格式的文本
     * @param replacements 占位符替换映射
     * @return Adventure Component
     */
    public static Component parse(String text, java.util.Map<String, Object> replacements) {
        if (text == null || text.isEmpty()) {
            return Component.empty();
        }
        
        try {
            MineDown mineDown = new MineDown(text);
            
            // 应用占位符替换
            if (replacements != null && !replacements.isEmpty()) {
                for (java.util.Map.Entry<String, Object> entry : replacements.entrySet()) {
                    String placeholder = entry.getKey();
                    Object value = entry.getValue();
                    
                    if (value instanceof Component) {
                        mineDown.replace(placeholder, (Component) value);
                    } else {
                        mineDown.replace(placeholder, String.valueOf(value));
                    }
                }
            }
            
            return mineDown.toComponent();
        } catch (Exception e) {
            // 如果解析失败，返回纯文本
            return Component.text(text);
        }
    }
    
    /**
     * 检查文本是否包含MineDown格式代码
     * 
     * @param text 要检查的文本
     * @return 如果包含MineDown格式代码则返回true
     */
    public static boolean containsMineDownFormat(String text) {
        if (text == null || text.isEmpty()) {
            return false;
        }
        
        // 检查常见的MineDown格式
        return text.contains("&") ||           // 颜色代码
               text.contains("**") ||          // 粗体
               text.contains("##") ||          // 斜体
               text.contains("__") ||          // 下划线
               text.contains("~~") ||          // 删除线
               text.contains("??") ||          // 混淆
               text.contains("[") ||           // 链接/事件
               text.contains("&#");            // RGB颜色
    }
    
    /**
     * 为死亡消息应用MineDown格式
     * 
     * @param playerName 玩家名
     * @param killerName 击杀者名（可为null）
     * @param weaponName 武器名（可为null）
     * @param deathCause 死亡原因
     * @return 格式化的死亡消息Component
     */
    public static Component formatDeathMessage(String playerName, String killerName, String weaponName, String deathCause) {
        StringBuilder message = new StringBuilder();
        
        switch (deathCause.toLowerCase()) {
            case "player":
            case "player_weapon":
                if (killerName != null) {
                    if (weaponName != null && !weaponName.isEmpty()) {
                        // PvP with weapon: PlayerA 被 PlayerB 用 [武器] 杀死了
                        message.append("&c").append(playerName)
                               .append(" &7被 &c").append(killerName)
                               .append(" &7用 &6[").append(weaponName).append("] &7杀死了");
                    } else {
                        // PvP without weapon: PlayerA 被 PlayerB 杀死了
                        message.append("&c").append(playerName)
                               .append(" &7被 &c").append(killerName)
                               .append(" &7杀死了");
                    }
                } else {
                    message.append("&c").append(playerName).append(" &7死了");
                }
                break;
                
            case "fall":
                message.append("&c").append(playerName).append(" &7落地过猛");
                break;
                
            case "drown":
                message.append("&c").append(playerName).append(" &9淹死了");
                break;
                
            case "lava":
                message.append("&c").append(playerName).append(" &6试图在熔岩里游泳");
                break;
                
            case "fire":
                message.append("&c").append(playerName).append(" &6被烧死了");
                break;
                
            case "explosion":
                message.append("&c").append(playerName).append(" &e爆炸了");
                break;
                
            case "void":
                message.append("&c").append(playerName).append(" &5掉出了这个世界");
                break;
                
            case "zombie":
                message.append("&c").append(playerName).append(" &2被僵尸杀死了");
                break;
                
            case "skeleton":
                message.append("&c").append(playerName).append(" &f被骷髅杀死了");
                break;
                
            case "creeper":
                message.append("&c").append(playerName).append(" &a被苦力怕炸死了");
                break;
                
            default:
                message.append("&c").append(playerName).append(" &7死了");
                break;
        }
        
        return parse(message.toString());
    }
    
    /**
     * 为死亡消息应用高级MineDown格式（支持渐变色和特效）
     * 
     * @param playerName 玩家名
     * @param killerName 击杀者名（可为null）
     * @param weaponName 武器名（可为null）
     * @param deathCause 死亡原因
     * @return 格式化的死亡消息Component
     */
    public static Component formatAdvancedDeathMessage(String playerName, String killerName, String weaponName, String deathCause) {
        StringBuilder message = new StringBuilder();
        
        switch (deathCause.toLowerCase()) {
            case "player":
            case "player_weapon":
                if (killerName != null) {
                    if (weaponName != null && !weaponName.isEmpty()) {
                        // PvP with weapon: 渐变色效果
                        message.append("&#ff6b6b-#ee5a52&").append(playerName)
                               .append(" &7被 &#4ecdc4-#44a08d&").append(killerName)
                               .append(" &7用 &#ffd93d-#6bcf7f&[").append(weaponName).append("] &7杀死了");
                    } else {
                        // PvP without weapon
                        message.append("&#ff6b6b-#ee5a52&").append(playerName)
                               .append(" &7被 &#4ecdc4-#44a08d&").append(killerName)
                               .append(" &7杀死了");
                    }
                } else {
                    message.append("&#ff6b6b-#ee5a52&").append(playerName).append(" &7死了");
                }
                break;
                
            case "fall":
                message.append("&#ff9a9e-#fecfef&").append(playerName).append(" &7落地过猛");
                break;
                
            case "drown":
                message.append("&#a8edea-#fed6e3&").append(playerName).append(" &9淹死了");
                break;
                
            case "lava":
                message.append("&#ff9a56-#ff6b35&").append(playerName).append(" &6试图在熔岩里游泳");
                break;
                
            case "fire":
                message.append("&#ff9a56-#ff6b35&").append(playerName).append(" &6被烧死了");
                break;
                
            case "explosion":
                message.append("&#ffd93d-#6bcf7f&").append(playerName).append(" &e爆炸了");
                break;
                
            case "void":
                message.append("&#667eea-#764ba2&").append(playerName).append(" &5掉出了这个世界");
                break;
                
            case "zombie":
                message.append("&#ff6b6b-#ee5a52&").append(playerName).append(" &2被僵尸杀死了");
                break;
                
            case "skeleton":
                message.append("&#ff6b6b-#ee5a52&").append(playerName).append(" &f被骷髅杀死了");
                break;
                
            case "creeper":
                message.append("&#ff6b6b-#ee5a52&").append(playerName).append(" &a被苦力怕炸死了");
                break;
                
            default:
                message.append("&rainbow&").append(playerName).append(" &7死了");
                break;
        }
        
        return parse(message.toString());
    }
}
