package org.plugin.messagetools.paper.listener;

import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.plugin.messagetools.paper.service.DeathMessageService;

import java.util.logging.Logger;

/**
 * 死亡事件监听器
 * 负责监听玩家死亡事件
 */
public class DeathEventListener implements Listener {
    
    private final DeathMessageService deathMessageService;
    private final Logger logger;
    
    public DeathEventListener(DeathMessageService deathMessageService, Logger logger) {
        this.deathMessageService = deathMessageService;
        this.logger = logger;
    }
    
    /**
     * 监听玩家死亡事件 - 高优先级处理
     * 使用HIGHEST优先级确保在其他插件处理之前获取原始信息
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerDeathHighest(PlayerDeathEvent event) {
        try {
            // 检查死亡消息服务是否启用
            if (!deathMessageService.isEnabled()) {
                return;
            }

            // 处理死亡事件（分析死亡原因和发送消息）
            deathMessageService.handlePlayerDeath(event);

        } catch (Exception e) {
            logger.severe("处理玩家死亡事件时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 监听玩家死亡事件 - 监控优先级
     * 使用MONITOR优先级确保在所有插件处理完后取消原始死亡消息
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerDeathMonitor(PlayerDeathEvent event) {
        try {
            // 检查死亡消息服务是否启用
            if (!deathMessageService.isEnabled()) {
                return;
            }

            // 检查是否应该取消原始死亡消息
            if (deathMessageService.shouldCancelOriginalMessage()) {
                // 使用新的API完全清空死亡消息
                event.deathMessage(null);

                if (deathMessageService.isDebugEnabled()) {
                    logger.info("已取消原始死亡消息: " + event.getEntity().getName());
                }
            }

        } catch (Exception e) {
            logger.severe("取消死亡消息时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
