# MessageTools 新架构完成 - Paper端构建，Velocity端转发

## 🎯 新架构设计理念

根据你的建议，我们采用了全新的架构设计：

### Paper端职责（完全负责死亡消息构建）
1. **原生死亡消息获取**：直接使用 `event.deathMessage()` 获取原生死亡消息
2. **保持悬停效果**：保留Minecraft原生的JSON悬停显示效果
3. **JSON序列化**：将完整的Component转换为JSON发送
4. **取消原始消息**：阻止原始死亡消息在本服务器显示

### Velocity端职责（纯转发功能）
1. **JSON反序列化**：将JSON转换回Component对象
2. **跨服广播**：转发到所有其他服务器
3. **配置控制**：简单的开关和广播范围控制

## 🔧 技术实现

### Paper端核心方法

#### 1. 获取原生死亡消息
```java
private net.kyori.adventure.text.Component buildCompleteDeathMessage(PlayerDeathEvent event) {
    // 获取原始死亡消息（如果存在）
    net.kyori.adventure.text.Component originalMessage = event.deathMessage();
    
    if (originalMessage != null) {
        // 直接使用原生死亡消息（保持悬停效果）
        return originalMessage;
    }
    
    // 如果没有原始死亡消息，手动构建
    return buildCustomDeathMessage(event);
}
```

#### 2. 保持武器悬停效果
```java
private net.kyori.adventure.text.Component buildPvPDeathMessage(Player victim, Player killer) {
    ItemStack weapon = killer.getInventory().getItemInMainHand();
    
    if (weapon != null && weapon.getType() != Material.AIR) {
        net.kyori.adventure.text.Component weaponComponent;
        
        if (weapon.hasItemMeta() && weapon.getItemMeta().hasDisplayName()) {
            // 使用武器的显示名称（保持原生悬停效果）
            weaponComponent = weapon.getItemMeta().displayName();
        } else {
            weaponComponent = net.kyori.adventure.text.Component.text(weapon.getType().name());
        }
        
        return net.kyori.adventure.text.Component.text()
            .append(net.kyori.adventure.text.Component.text(victim.getName()))
            .append(net.kyori.adventure.text.Component.text(" 被 "))
            .append(net.kyori.adventure.text.Component.text(killer.getName()))
            .append(net.kyori.adventure.text.Component.text(" 用 "))
            .append(weaponComponent)  // 保持原生悬停效果
            .append(net.kyori.adventure.text.Component.text(" 杀死了"))
            .build();
    }
}
```

#### 3. JSON序列化发送
```java
// 将Component序列化为JSON
String messageJson = net.kyori.adventure.text.serializer.gson.GsonComponentSerializer.gson().serialize(deathMessage);

// 发送格式：playerName|messageJson|serverName
String messageData = playerName + "|" + messageJson + "|" + serverName;
```

### Velocity端核心方法

#### 1. JSON反序列化
```java
public void handleCompleteDeathMessage(String playerName, String messageJson, String sourceServer) {
    // 将JSON反序列化为Component
    net.kyori.adventure.text.Component deathMessage = 
        net.kyori.adventure.text.serializer.gson.GsonComponentSerializer.gson().deserialize(messageJson);
    
    // 广播到所有玩家
    broadcastCompleteDeathMessage(deathMessage, sourceServer);
}
```

#### 2. 跨服广播
```java
private void broadcastCompleteDeathMessage(net.kyori.adventure.text.Component deathMessage, String sourceServer) {
    boolean broadcastToAll = configManager.getBoolean("death_messages.broadcast_to_all_servers", true);
    boolean includeSource = configManager.getBoolean("death_messages.include_source_server", true);
    
    if (broadcastToAll) {
        server.getAllPlayers().forEach(player -> {
            if (!includeSource && player.getCurrentServer().isPresent() && 
                player.getCurrentServer().get().getServerInfo().getName().equals(sourceServer)) {
                return; // 跳过来源服务器的玩家
            }
            
            player.sendMessage(deathMessage);  // 保持完整的悬停效果
        });
    }
}
```

## 🎮 新架构优势

### 1. **完美的原生兼容性**
- ✅ 保持Minecraft原生的死亡消息格式
- ✅ 保留武器的悬停显示效果
- ✅ 支持所有原版死亡原因
- ✅ 自动处理附魔信息显示

### 2. **简化的架构**
- ✅ Paper端：完全负责死亡消息构建
- ✅ Velocity端：纯转发功能，无需复杂配置
- ✅ 消息格式：简单的3段格式 `playerName|messageJson|serverName`

### 3. **技术优势**
- ✅ 避免TextComponent序列化问题
- ✅ 保持原生JSON悬停效果
- ✅ 无需手动翻译附魔名称
- ✅ 自动支持自定义武器名称

### 4. **配置简化**
- ✅ Velocity端只需要简单的开关配置
- ✅ Paper端无需复杂的死亡原因映射
- ✅ 自动适配所有死亡情况

## 📋 消息格式对比

### 旧架构（复杂5段格式）
```
playerName|deathCause|killerName|weapon|serverName
viagar|player_weapon|NSrank|TextComponentImpl{...}|newnanserver
```

### 新架构（简单3段格式）
```
playerName|messageJson|serverName
viagar|{"text":"viagar 被 NSrank 用 ","extra":[{"text":"天丛云剑","color":"red","hoverEvent":{...}}],"text":" 杀死了"}|newnanserver
```

## 🚀 部署说明

### 1. 文件位置
- **Velocity端**：`MessageTools-1.7-SNAPSHOT.jar`
- **Paper端**：`messagetools-paper-1.0-SNAPSHOT.jar`

### 2. 配置更新
#### Velocity端配置（简化）
```yaml
death_messages:
  enabled: true
  broadcast_to_all_servers: true
  include_source_server: true
  console_output: true

debug:
  enabled: true  # 临时启用查看效果
  verbose_events: true
```

#### Paper端配置（保持不变）
```yaml
death_messages:
  enabled: true
  cancel_original: true
```

### 3. 预期效果

#### 原生死亡消息
- **空手PvP**：`PlayerA 被 PlayerB 杀死了`
- **武器PvP**：`PlayerA 被 PlayerB 用 [钻石剑] 杀死了`（带悬停效果）
- **自定义武器**：`PlayerA 被 PlayerB 用 [天丛云剑] 杀死了`（带悬停效果）
- **附魔武器**：武器悬停显示完整的附魔信息

#### 环境死亡
- **摔死**：`PlayerA 落地过猛`
- **溺水**：`PlayerA 淹死了`
- **岩浆**：`PlayerA 试图在熔岩里游泳`

## ✅ 验证方法

### 1. 启用调试模式
在Velocity端配置中设置 `debug.enabled: true`

### 2. 查看日志
```
[INFO] 处理完整死亡消息: 玩家=viagar, 来源服务器=newnanserver
[INFO] 成功反序列化死亡消息Component
[INFO] 广播死亡消息 - 全服广播: true, 包含来源: true
[INFO] 死亡消息已广播到所有玩家
[INFO] [死亡消息] viagar 被 NSrank 用 天丛云剑 杀死了
[INFO] 完整死亡消息处理完成
```

### 3. 测试悬停效果
- 在游戏中查看死亡消息
- 鼠标悬停在武器名称上
- 应该显示完整的武器信息（附魔、耐久等）

## 🎯 关键改进

1. **原生兼容性**：完全使用Minecraft原生的死亡消息系统
2. **悬停效果保持**：武器信息保持原生的JSON悬停显示
3. **架构简化**：Paper端负责构建，Velocity端负责转发
4. **配置简化**：无需复杂的死亡原因映射和格式配置
5. **自动适配**：自动支持所有原版死亡情况和自定义武器

现在MessageTools使用全新的架构，完美保持了Minecraft原生的死亡消息体验，包括武器的悬停效果和附魔信息显示！🎮
