# MessageTools PvP死亡消息最终修复

## 🔍 问题根本原因

从最新的日志分析发现了问题的根本原因：

### 1. **Adventure API导入缺失**
- **问题**：`DeathCauseAnalyzer.java` 缺少Adventure API的导入
- **现象**：TextComponent无法转换为纯文本，仍然发送序列化对象
- **修复**：添加了必要的导入和正确的API调用

### 2. **消息重复处理**
- **问题**：两个事件监听器都在处理死亡事件
- **现象**：每个死亡事件被处理两次，导致重复日志
- **说明**：这是设计行为（HIGHEST获取信息，MONITOR取消原始消息）

### 3. **编译部署问题**
- **问题**：修复的代码没有正确编译或部署
- **现象**：服务器仍在使用旧版本的JAR文件
- **修复**：重新编译并生成新的JAR文件

## 🔧 最终修复内容

### Paper端修复 (DeathCauseAnalyzer.java)

#### 1. 添加Adventure API导入
```java
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.serializer.plain.PlainTextComponentSerializer;
```

#### 2. 修复TextComponent转换
```java
// 将Adventure Component转换为纯文本
Component displayName = weapon.getItemMeta().displayName();
if (displayName != null) {
    return PlainTextComponentSerializer.plainText().serialize(displayName);
}
```

#### 3. 修复空手攻击
```java
return null; // 空手攻击，不返回武器名称
```

### Paper端修复 (DeathMessageService.java)

#### 4. 添加服务器名称
```java
// 获取服务器名称
String serverName = plugin.getServer().getName();
if (serverName == null || serverName.isEmpty()) {
    serverName = "unknown";
}

// 构建5段格式消息
String messageData = playerName + "|" + deathCause + "|" + 
                   (killerName != null ? killerName : "") + "|" + 
                   (weapon != null ? weapon : "") + "|" + serverName;
```

### Velocity端修复 (DeathMessageService.java)

#### 5. 增强调试日志
```java
if (debugEnabled) {
    logger.info("构建死亡消息 - 原因: {}, 格式: {}", deathCause, format);
    logger.info("变量 - 玩家: {}, 杀手: {}, 武器: {}", playerName, killerName, weapon);
}
```

#### 6. 改进消息解析
```java
if (debugEnabled && verboseEvents) {
    logger.info("解析插件消息 - 段数: {}", parts.length);
    logger.info("  玩家: {}", playerName);
    logger.info("  死亡原因: {}", deathCause);
    logger.info("  杀手: {}", killerName);
    logger.info("  武器: {}", weapon);
    logger.info("  服务器: {}", sourceServer);
}
```

## 📦 需要部署的文件

### 1. Velocity端
- **文件**：`MessageTools-1.7-SNAPSHOT.jar`
- **位置**：`d:\MinecraftPlugins\MessageTools\target\MessageTools-1.7-SNAPSHOT.jar`
- **部署到**：Velocity服务器的 `plugins` 目录

### 2. Paper端
- **文件**：`messagetools-paper-1.0-SNAPSHOT.jar`
- **位置**：`d:\MinecraftPlugins\MessageTools-Paper\target\messagetools-paper-1.0-SNAPSHOT.jar`
- **部署到**：每个Paper服务器的 `plugins` 目录

## 🎯 预期修复效果

### 修复前的问题日志
```
收到来自服务器 newnanserver 的死亡消息: viagar|player_weapon|NSrank|TextComponentImpl{content="天丛云剑"...}
[死亡消息] viagar 死了
```

### 修复后的预期日志
```
解析插件消息 - 段数: 5
  玩家: viagar
  死亡原因: player_weapon
  杀手: NSrank
  武器: 天丛云剑
  服务器: newnanserver
查找死亡格式 - 路径: death_messages.formats.player_weapon, 结果: &f%player% &7被 &f%killer% &7用 &f%weapon% &7杀死了
构建死亡消息 - 原因: player_weapon, 格式: &f%player% &7被 &f%killer% &7用 &f%weapon% &7杀死了
变量 - 玩家: viagar, 杀手: NSrank, 武器: 天丛云剑
最终死亡消息: viagar 被 NSrank 用 天丛云剑 杀死了
[死亡消息] viagar 被 NSrank 用 天丛云剑 杀死了
```

## 🔍 测试验证步骤

### 1. 启用调试模式
在Velocity端配置文件中设置：
```yaml
debug:
  enabled: true
  verbose_events: true
```

### 2. 测试场景

#### 空手PvP测试
- **操作**：PlayerB空手击杀PlayerA
- **预期消息**：`PlayerA 被 PlayerB 杀死了`
- **预期日志**：武器字段为空，使用 `player` 格式

#### 武器PvP测试
- **操作**：PlayerB用钻石剑击杀PlayerA
- **预期消息**：`PlayerA 被 PlayerB 用 钻石剑 杀死了`
- **预期日志**：武器字段显示 `钻石剑`，使用 `player_weapon` 格式

#### 自定义武器测试
- **操作**：PlayerB用自定义名称武器（如"天丛云剑"）击杀PlayerA
- **预期消息**：`PlayerA 被 PlayerB 用 天丛云剑 杀死了`
- **预期日志**：武器字段显示纯文本 `天丛云剑`，不是TextComponent

#### 附魔武器测试
- **操作**：PlayerB用附魔武器击杀PlayerA
- **预期消息**：`PlayerA 被 PlayerB 用 锋利Ⅴ钻石剑 杀死了`
- **预期日志**：武器字段包含附魔信息

### 3. 验证要点
- ✅ 武器信息是纯文本，不是TextComponent对象
- ✅ 消息格式是5段，包含服务器名称
- ✅ 死亡消息格式正确匹配（player, player_weapon等）
- ✅ 杀手名称正确显示
- ✅ 武器名称正确显示（包括自定义名称和附魔）
- ✅ 空手攻击不显示武器信息

## 🚀 部署说明

1. **停止服务器**：先停止Velocity和所有Paper服务器
2. **备份旧文件**：备份现有的插件JAR文件
3. **部署新文件**：将新编译的JAR文件复制到对应目录
4. **启动服务器**：先启动Velocity，再启动Paper服务器
5. **启用调试**：临时启用调试模式验证修复效果
6. **测试验证**：进行各种PvP测试确认功能正常

## ✅ 修复确认

修复成功的标志：
- ✅ 武器信息显示为纯文本而不是TextComponent
- ✅ PvP死亡消息正确显示杀手和武器信息
- ✅ 空手攻击不显示武器信息
- ✅ 自定义武器名称正确提取
- ✅ 附魔信息正确显示
- ✅ 消息不再重复处理（虽然有两个监听器，但功能不同）

现在MessageTools的PvP死亡消息功能应该完全正常工作了！🎮
