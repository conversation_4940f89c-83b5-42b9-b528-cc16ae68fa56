# MessageTools 配置文件
# 支持原版颜色代码 (&a, &b, &c等) 和格式代码 (&l, &n, &o等)
# 支持PlaceholderAPI变量 (需要PAPIProxyBridge)

# 消息配置
messages:
  # 玩家加入群组服时在全服聊天中发送的消息
  join:
    enabled: true
    tick: 20  # 玩家加入后20tick(1秒)后发送
    messages:
      - "&a[+] &f%player_name% &7加入了服务器"
      - "&7欢迎 &a%player_name% &7来到我们的服务器！"

  # 玩家退出群组服时在全服聊天中发送的消息
  quit:
    enabled: true
    tick: 0  # 立即发送
    messages:
      - "&c[-] &f%player_name% &7离开了服务器"

  # 玩家加入时向玩家发送的私人消息
  message:
    enabled: true
    tick: 60  # 玩家加入后60tick(3秒)后发送
    messages:
      - "&a欢迎来到服务器，%player_name%！"
      - "&7当前在线玩家数：&a%server_online%"
      - "&7服务器时间：&e%server_time_HH:mm:ss%"

  # 玩家第一次加入时在全服聊天中发送的消息
  first_join:
    enabled: true
    tick: 10  # 玩家加入后10tick(0.5秒)后发送，优先于普通加入消息
    messages:
      - "&6★ &f%player_name% &e第一次加入了服务器！"
      - "&e让我们热烈欢迎新玩家！"

  # 玩家第一次退出时在全服聊天中发送的消息
  first_quit:
    enabled: true
    tick: 0  # 立即发送
    messages:
      - "&6★ &f%player_name% &e第一次离开了服务器"

# 死亡消息配置
death_messages:
  # 是否启用死亡消息功能
  enabled: true
  # 是否广播到所有服务器
  broadcast_to_all_servers: true
  # 是否包含来源服务器的玩家
  include_source_server: true
  # 是否在控制台输出死亡消息
  console_output: true
  # 默认死亡消息格式（使用原版格式）
  default_format: "&f%player% &7死了"

  # 死亡原因对应的消息格式（基于Minecraft原版死亡消息）
  formats:
    # 意外事故
    fall: "&f%player% &7落地过猛"
    stalagmite: "&f%player% &7被石笋刺穿了"

    # 危险环境
    drown: "&f%player% &7淹死了"
    lava: "&f%player% &7试图在熔岩里游泳"
    fire: "&f%player% &7浴火焚身"
    suffocation: "&f%player% &7在墙里窒息而亡"
    void: "&f%player% &7掉出了这个世界"
    cactus: "&f%player% &7被戳死了"
    hot_floor: "&f%player% &7发现了地板是熔岩做的"
    sweet_berry_bush: "&f%player% &7被甜浆果丛刺死了"
    lightning_bolt: "&f%player% &7被闪电击中"
    freeze: "&f%player% &7被冻死了"

    # 负面效果
    starve: "&f%player% &7饿死了"
    on_fire: "&f%player% &7被烧死了"
    magic: "&f%player% &7被魔法杀死了"
    wither: "&f%player% &7凋零了"
    poison: "&f%player% &7中毒身亡"

    # 生物攻击
    mob: "&f%player% &7被怪物杀死了"
    zombie: "&f%player% &7被僵尸杀死了"
    skeleton: "&f%player% &7被骷髅射死了"
    spider: "&f%player% &7被蜘蛛杀死了"
    creeper: "&f%player% &7被苦力怕炸死了"
    enderman: "&f%player% &7被末影人杀死了"

    # 玩家PvP（原版格式）
    player: "&f%player% &7被 &f%killer% &7杀死了"
    player_weapon: "&f%player% &7被 &f%killer% &7用 &f%weapon% &7杀死了"
    player_bow: "&f%player% &7被 &f%killer% &7射杀"
    player_bow_weapon: "&f%player% &7被 &f%killer% &7用 &f%weapon% &7射杀"

    # 爆炸
    explosion: "&f%player% &7爆炸了"
    tnt: "&f%player% &7被TNT炸死了"

    # 其他伤害类型
    projectile: "&f%player% &7被投射物杀死了"
    thorns: "&f%player% &7在试图伤害其他生物时被杀"
    sonic_boom: "&f%player% &7被一道音波尖啸抹除了"
    mace_smash: "&f%player% &7被重锤猛击致死"

    # 下落方块
    falling_anvil: "&f%player% &7被下落的铁砧压扁了"
    falling_stalactite: "&f%player% &7被下落的钟乳石刺穿了"
    falling_block: "&f%player% &7被下落的方块压扁了"

    # 特殊情况
    fly_into_wall: "&f%player% &7感受到了动能"
    cramming: "&f%player% &7因被过度挤压而死"
    fireworks: "&f%player% &7随着一声巨响消失了"
    bad_respawn_point: "&f%player% &7被[刻意的游戏设计]杀死了"

    # 未知原因
    unknown: "&f%player% &7死了"

# 控制台输出配置
console:
  # 是否在控制台输出玩家加入/退出信息
  enabled: true
  # 输出格式
  format:
    join: "[MessageTools] 玩家 %player_name% 加入了服务器 (IP: %player_ip%)"
    quit: "[MessageTools] 玩家 %player_name% 离开了服务器"
    first_join: "[MessageTools] 新玩家 %player_name% 第一次加入服务器"
    first_quit: "[MessageTools] 新玩家 %player_name% 第一次离开服务器"

# 消息发送延迟配置 (单位：毫秒)
delays:
  # 消息之间的发送间隔
  between_messages: 500
  # 玩家加入后的延迟发送时间
  after_join: 1000

# 数据存储配置
storage:
  # 玩家数据文件名
  player_data_file: "playerdata.yml"
  # 是否自动保存数据
  auto_save: true
  # 自动保存间隔 (单位：分钟)
  auto_save_interval: 5

# Unicode字符配置
unicode:
  # 是否启用Unicode字符支持
  enabled: true

  # 预定义Unicode字符映射
  characters:
    # 货币符号
    yen: "￥"
    naira: "₦"
    euro: "€"
    dollar: "$"
    pound: "£"

    # 游戏相关符号
    skull: "💀"
    sword: "⚔"
    bow: "🏹"
    fire: "🔥"
    explosion: "💥"
    heart: "❤"
    star: "⭐"

    # 箭头符号
    arrow_right: "→"
    arrow_left: "←"
    arrow_up: "↑"
    arrow_down: "↓"

# PlaceholderAPI集成配置
placeholderapi:
  # 是否启用PlaceholderAPI支持
  enabled: true
  # 变量解析超时时间 (单位：毫秒)
  timeout: 1000

# 调试配置
debug:
  # 是否启用调试模式
  enabled: false
  # 是否输出详细的事件处理日志
  verbose_events: false
  # 是否输出配置加载日志
  verbose_config: false
