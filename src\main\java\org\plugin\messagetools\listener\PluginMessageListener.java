package org.plugin.messagetools.listener;

import com.velocitypowered.api.event.Subscribe;
import com.velocitypowered.api.event.connection.PluginMessageEvent;
import com.velocitypowered.api.proxy.Player;
import com.velocitypowered.api.proxy.ServerConnection;
import com.velocitypowered.api.util.ModInfo;
import org.plugin.messagetools.service.DeathMessageService;
import org.slf4j.Logger;

import java.nio.charset.StandardCharsets;

/**
 * 插件消息监听器
 * 负责处理来自Paper端的插件消息
 */
public class PluginMessageListener {
    
    private final DeathMessageService deathMessageService;
    private final Logger logger;
    
    // 插件消息通道标识符
    public static final String DEATH_MESSAGE_CHANNEL = "messagetools:death";
    
    public PluginMessageListener(DeathMessageService deathMessageService, Logger logger) {
        this.deathMessageService = deathMessageService;
        this.logger = logger;
    }
    
    /**
     * 处理插件消息事件
     */
    @Subscribe
    public void onPluginMessage(PluginMessageEvent event) {
        // 检查是否是我们关心的消息通道
        if (!DEATH_MESSAGE_CHANNEL.equals(event.getIdentifier().getId())) {
            return;
        }
        
        // 确保消息来源是服务器连接
        if (!(event.getSource() instanceof ServerConnection)) {
            return;
        }
        
        ServerConnection serverConnection = (ServerConnection) event.getSource();
        String serverName = serverConnection.getServerInfo().getName();
        
        try {
            // 解析消息数据
            byte[] data = event.getData();
            String message = new String(data, StandardCharsets.UTF_8);
            
            boolean debugEnabled = logger.isDebugEnabled();
            if (debugEnabled) {
                logger.debug("收到来自服务器 {} 的死亡消息: {}", serverName, message);
            }
            
            // 检查死亡消息服务是否启用
            if (!deathMessageService.isEnabled()) {
                if (debugEnabled) {
                    logger.debug("死亡消息功能已禁用，忽略消息");
                }
                return;
            }
            
            // 处理死亡消息
            deathMessageService.processPluginMessage(message + "|" + serverName);
            
            // 阻止消息继续传播（避免重复处理）
            event.setResult(PluginMessageEvent.ForwardResult.handled());
            
        } catch (Exception e) {
            logger.error("处理来自服务器 {} 的插件消息时发生错误: {}", serverName, e.getMessage(), e);
        }
    }
    
    /**
     * 获取死亡消息通道标识符
     */
    public static String getDeathMessageChannel() {
        return DEATH_MESSAGE_CHANNEL;
    }
}
