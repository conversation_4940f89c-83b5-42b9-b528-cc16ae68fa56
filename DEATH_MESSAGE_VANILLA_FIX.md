# MessageTools 死亡消息原版格式修复

## 🎯 修复内容

根据用户要求，已将死亡消息从带Unicode符号的自定义格式改为Minecraft原版的中文死亡消息格式。

### 📋 主要修改

1. **移除Unicode符号**：不再使用 `{unicode:skull}`、`{unicode:sword}` 等符号
2. **使用原版格式**：采用Minecraft官方中文死亡消息的表述方式
3. **保持功能完整**：跨服务器广播功能保持不变

## 🔧 修复的技术问题

### Velocity端修复
1. ✅ **插件消息通道注册**：正确使用 `MinecraftChannelIdentifier.from()`
2. ✅ **配置文件结构**：恢复正确的Velocity端配置文件格式
3. ✅ **死亡消息处理**：增强调试日志和错误处理

### Paper端修复
1. ✅ **双重事件监听**：
   - `HIGHEST` 优先级：获取死亡信息并发送到代理
   - `MONITOR` 优先级：取消原始死亡消息显示
2. ✅ **API兼容性**：使用 `event.deathMessage(null)` 完全阻止原始死亡消息
3. ✅ **枚举常量修复**：修复 `EXPLOSION` → `ENTITY_EXPLOSION` 等问题

## 📝 原版死亡消息格式

### 基础格式
```yaml
# 不使用Unicode符号，采用原版表述
fall: "&f%player% &7落地过猛"
drown: "&f%player% &7淹死了"
lava: "&f%player% &7试图在熔岩里游泳"
```

### 完整配置示例
```yaml
death_messages:
  enabled: true
  broadcast_to_all_servers: true
  include_source_server: true
  console_output: true
  default_format: "&f%player% &7死了"
  
  formats:
    # 意外事故
    fall: "&f%player% &7落地过猛"
    stalagmite: "&f%player% &7被石笋刺穿了"
    
    # 危险环境
    drown: "&f%player% &7淹死了"
    lava: "&f%player% &7试图在熔岩里游泳"
    fire: "&f%player% &7浴火焚身"
    suffocation: "&f%player% &7在墙里窒息而亡"
    void: "&f%player% &7掉出了这个世界"
    cactus: "&f%player% &7被戳死了"
    
    # 负面效果
    starve: "&f%player% &7饿死了"
    on_fire: "&f%player% &7被烧死了"
    magic: "&f%player% &7被魔法杀死了"
    wither: "&f%player% &7凋零了"
    
    # 生物攻击
    mob: "&f%player% &7被怪物杀死了"
    zombie: "&f%player% &7被僵尸杀死了"
    skeleton: "&f%player% &7被骷髅射死了"
    spider: "&f%player% &7被蜘蛛杀死了"
    creeper: "&f%player% &7被苦力怕炸死了"
    enderman: "&f%player% &7被末影人杀死了"
    
    # 玩家PvP（原版格式）
    player: "&f%player% &7被 &f%weapon% &7杀死了"
    player_bow: "&f%player% &7被 &f%weapon% &7射杀"
    
    # 爆炸
    explosion: "&f%player% &7爆炸了"
    tnt: "&f%player% &7被TNT炸死了"
    
    # 其他伤害类型
    projectile: "&f%player% &7被投射物杀死了"
    thorns: "&f%player% &7在试图伤害其他生物时被杀"
    sonic_boom: "&f%player% &7被一道音波尖啸抹除了"
    mace_smash: "&f%player% &7被重锤猛击致死"
    
    # 下落方块
    falling_anvil: "&f%player% &7被下落的铁砧压扁了"
    falling_stalactite: "&f%player% &7被下落的钟乳石刺穿了"
    falling_block: "&f%player% &7被下落的方块压扁了"
    
    # 特殊情况
    fly_into_wall: "&f%player% &7感受到了动能"
    cramming: "&f%player% &7因被过度挤压而死"
    fireworks: "&f%player% &7随着一声巨响消失了"
    bad_respawn_point: "&f%player% &7被[刻意的游戏设计]杀死了"
    
    # 未知原因
    unknown: "&f%player% &7死了"
```

## 🎮 实际效果对比

### 修复前（带Unicode符号）
```
💀 PlayerA 摔死了
⚔ PlayerB 被 钻石剑 杀死了
💥 PlayerC 被苦力怕炸死了
```

### 修复后（原版格式）
```
PlayerA 落地过猛
PlayerB 被 钻石剑 杀死了
PlayerC 被苦力怕炸死了
```

## 📦 生成的文件

### Velocity端
- **文件**：`MessageTools-1.7-SNAPSHOT.jar`
- **位置**：`d:\MinecraftPlugins\MessageTools\target\`
- **功能**：处理死亡消息广播，使用原版格式

### Paper端
- **文件**：`messagetools-paper-1.0-SNAPSHOT.jar`
- **位置**：`d:\MinecraftPlugins\MessageTools-Paper\target\`
- **功能**：监听死亡事件，取消原始消息，发送到代理

## 🚀 部署说明

1. **Velocity端**：
   - 将 `MessageTools-1.7-SNAPSHOT.jar` 放入 Velocity 的 `plugins` 目录
   - 配置文件会自动生成，包含原版死亡消息格式

2. **Paper端**：
   - 将 `messagetools-paper-1.0-SNAPSHOT.jar` 放入每个 Paper 服务器的 `plugins` 目录
   - 配置文件会自动生成，默认取消原始死亡消息

3. **重启服务器**：
   - 先重启 Velocity 代理
   - 再重启所有 Paper 服务器

## ✅ 验证方法

1. **测试死亡消息**：
   - 在任意服务器中让玩家死亡
   - 检查是否显示原版格式的死亡消息
   - 确认原始死亡消息已被取消

2. **检查跨服广播**：
   - 确认死亡消息在所有服务器显示
   - 验证消息格式符合原版标准

3. **调试模式**：
   - 如有问题，可启用调试模式查看详细日志
   - 检查插件消息通道是否正常工作

## 🎯 关键改进

1. **原版兼容性**：完全符合Minecraft原版死亡消息的表述方式
2. **简洁清晰**：移除了可能造成显示问题的Unicode符号
3. **功能完整**：保持所有跨服务器广播功能
4. **稳定可靠**：修复了所有已知的技术问题

现在MessageTools的死亡消息功能使用纯正的Minecraft原版中文格式，确保最佳的兼容性和用户体验！
