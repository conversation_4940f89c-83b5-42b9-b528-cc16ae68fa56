#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x000001dc07dd3c23, pid=9696, tid=9364
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# Problematic frame:
# J 6786 c2 org.eclipse.jdt.internal.core.index.MemoryIndex.addIndexEntry([C[CLjava/lang/String;)V (118 bytes) @ 0x000001dc07dd3c23 [0x000001dc07dd2f40+0x0000000000000ce3]
#
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://github.com/adoptium/adoptium-support/issues
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8549aeba6249e2d1a9ef117416a9d08e\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8549aeba6249e2d1a9ef117416a9d08e\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-43fa0094dfa2d4a981dce5d72ba972d4-sock

Host: Intel(R) Core(TM) i5-1035G1 CPU @ 1.00GHz, 8 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Fri Jun 27 15:02:26 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.4202) elapsed time: 190.587746 seconds (0d 0h 3m 10s)

---------------  T H R E A D  ---------------

Current thread (0x000001dc587b9e10):  JavaThread "Java indexing" daemon [_thread_in_Java, id=9364, stack(0x000000d9af600000,0x000000d9af700000) (1024K)]

Stack: [0x000000d9af600000,0x000000d9af700000],  sp=0x000000d9af6feb30,  free space=1018k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
C  0x000001dc07dd3c23

The last pc belongs to nmethod (printed below).

siginfo: EXCEPTION_ACCESS_VIOLATION (0xc0000005), reading address 0x0000000000000014


Registers:
RAX=0x0000000000000000, RBX=0x000001dc78451000, RCX=0x0000000000000008, RDX=0x000000009e318aa8
RSP=0x000000d9af6feb30, RBP=0x00000000d57e4218, RSI=0x00000000d57e4218, RDI=0x0000000000000008
R8 =0x000000000003480e, R9 =0x0000000000035ffd, R10=0x000000009e318aa8, R11=0x000000009f86b398
R12=0x0000000000000000, R13=0x0000000000000001, R14=0x0000000000000008, R15=0x000001dc587b9e10
RIP=0x000001dc07dd3c23, EFLAGS=0x0000000000010206

XMM[0]=0x0000000000000000 0x0000000000000000
XMM[1]=0x0000000000000000 0x0000000000000000
XMM[2]=0x0000000000000000 0x00000000d57e41c8
XMM[3]=0x7dd7bc017dd7bc01 0x7dd7bc017dd7bc01
XMM[4]=0x294fe48100acab9f 0x14e8c84188303fdf
XMM[5]=0x0c98f5818685ba9f 0x4a319941fc018edf
XMM[6]=0xee830681e1ddc99f 0x59db6a41e191dddf
XMM[7]=0x000e178101b4d89f 0x34e63b4167e12cdf
XMM[8]=0x6c5b0f2e498512b3 0x10eed42faaa8b33d
XMM[9]=0x437c1d2ec06abb14 0x971e40ad20baf888
XMM[10]=0x9f46be6c9509653f 0x9c47a7ee72f4cd4e
XMM[11]=0x02c09732c95bd94a 0x815ea5ae93f5871c
XMM[12]=0x8c8bd6008c8bd600 0x8c8bd6006953cc7d
XMM[13]=0x0000000000000000 0x00000000dcc7f67d
XMM[14]=0xe1874270e1874270 0xe18742704adb0eed
XMM[15]=0x0000000000000000 0x000000006953cc7d
  MXCSR=0x00001fa0


Register to memory mapping:

RAX=0x0 is null
RBX=0x000001dc78451000 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
RCX=0x0000000000000008 is an unknown value
RDX=0x000000009e318aa8 is an oop: org.eclipse.jdt.internal.core.util.SimpleWordSet 
{0x000000009e318aa8} - klass: 'org/eclipse/jdt/internal/core/util/SimpleWordSet'
 - ---- fields (total size 3 words):
 - public 'elementSize' 'I' @12  123618 (0x0001e2e2)
 - public 'threshold' 'I' @16  147454 (0x00023ffe)
 - public 'words' '[[C' @20  a {type array char}[221181] {0x000000009f86b398} (0x9f86b398)
RSP=0x000000d9af6feb30 is pointing into the stack for thread: 0x000001dc587b9e10
RBP=0x00000000d57e4218 is an oop: [C 
{0x00000000d57e4218} - klass: {type array char}
 - length: 9
RSI=0x00000000d57e4218 is an oop: [C 
{0x00000000d57e4218} - klass: {type array char}
 - length: 9
RDI=0x0000000000000008 is an unknown value
R8 =0x000000000003480e is an unknown value
R9 =0x0000000000035ffd is an unknown value
R10=0x000000009e318aa8 is an oop: org.eclipse.jdt.internal.core.util.SimpleWordSet 
{0x000000009e318aa8} - klass: 'org/eclipse/jdt/internal/core/util/SimpleWordSet'
 - ---- fields (total size 3 words):
 - public 'elementSize' 'I' @12  123618 (0x0001e2e2)
 - public 'threshold' 'I' @16  147454 (0x00023ffe)
 - public 'words' '[[C' @20  a {type array char}[221181] {0x000000009f86b398} (0x9f86b398)
R11=0x000000009f86b398 is an oop: [[C 
{0x000000009f86b398} - klass: {type array char}[]
 - length: 221181
R12=0x0 is null
R13=0x0000000000000001 is an unknown value
R14=0x0000000000000008 is an unknown value
R15=0x000001dc587b9e10 is a thread

Top of Stack: (sp=0x000000d9af6feb30)
0x000000d9af6feb30:   00000000814a89f8 000000009e314738
0x000000d9af6feb40:   00000000d57e4318 00000000d57e4218
0x000000d9af6feb50:   0000000000000001 00000000d57e4318
0x000000d9af6feb60:   00000000d57dd3b8 0000000081490838
0x000000d9af6feb70:   00000000d57e2d78 000001dc080c2a9c
0x000000d9af6feb80:   0000000000000000 000000080000000e
0x000000d9af6feb90:   00000000d57dd3b8 000001dc085db0e8
0x000000d9af6feba0:   00000000d57e4218 00000000d57e42d0
0x000000d9af6febb0:   0000000000000000 000000000000001a
0x000000d9af6febc0:   0000000000000001 0000000000000003
0x000000d9af6febd0:   0000000000000005 000001dc08601c70
0x000000d9af6febe0:   0000000000000002 0000000500000000
0x000000d9af6febf0:   0000000000000012 0000000000000000
0x000000d9af6fec00:   0000000000000000 d57e2d5800000000
0x000000d9af6fec10:   00000000d57dd3c8 000000008045d170
0x000000d9af6fec20:   0000000000000004 00000000d57e1930
0x000000d9af6fec30:   00000000d57dd3b8 d57e2d78d57e1930
0x000000d9af6fec40:   00000000d57e2d58 00000000d57e2d78
0x000000d9af6fec50:   00000000d57e4218 00000000d57e2e98
0x000000d9af6fec60:   00000000d57e3128 00000001d57e2f10
0x000000d9af6fec70:   0000000000000001 0000000000000045
0x000000d9af6fec80:   00000000d57e2f10 0000000080ce1e60
0x000000d9af6fec90:   0000000000000000 00000000d578b008
0x000000d9af6feca0:   00000000a02867d8 00000000d57e3f48
0x000000d9af6fecb0:   0000000081490838 0000000080ce1e70
0x000000d9af6fecc0:   0000000080ce1e70 000000000000004c
0x000000d9af6fecd0:   0000003fffffffff 0000000000000001
0x000000d9af6fece0:   0000000000000002 00000000d57e32f8
0x000000d9af6fecf0:   0000002900000027 00000000d57e31d8
0x000000d9af6fed00:   00000000d57db2b0 0000000000000068
0x000000d9af6fed10:   00000000d57e3310 00000000d57dd088
0x000000d9af6fed20:   0000000500000028 0000000081124348 

Instructions: (pc=0x000001dc07dd3c23)
0x000001dc07dd3b23:   c9 0f 84 91 00 00 00 48 8d 7f 10 48 8d 76 10 d1
0x000001dc07dd3b33:   e1 8b c1 83 e0 1f 83 e1 e0 0f 84 4b 00 00 00 48
0x000001dc07dd3b43:   8d 3c 0f 48 8d 34 0e 48 f7 d9 c5 fe 6f 04 0f c5
0x000001dc07dd3b53:   fe 6f 0c 0e c5 fd ef c1 c4 e2 7d 17 c0 0f 85 5c
0x000001dc07dd3b63:   00 00 00 48 83 c1 20 75 e1 85 c0 0f 84 47 00 00
0x000001dc07dd3b73:   00 c5 fe 6f 44 07 e0 c5 fe 6f 4c 06 e0 c5 fd ef
0x000001dc07dd3b83:   c1 c4 e2 7d 17 c0 75 37 eb 2e 8b c8 83 e1 fc 74
0x000001dc07dd3b93:   19 48 8d 3c 0f 48 8d 34 0e 48 f7 d9 8b 1c 0f 3b
0x000001dc07dd3ba3:   1c 0e 75 1b 48 83 c1 04 75 f2 a8 02 74 0a 0f b7
0x000001dc07dd3bb3:   1f 0f b7 0e 3b d9 75 07 b8 01 00 00 00 eb 02 33
0x000001dc07dd3bc3:   c0 c5 fd ef c0 c5 f5 ef c9 85 c0 0f 85 1f 01 00
0x000001dc07dd3bd3:   00 41 ff c0 45 3b c1 0f 84 09 01 00 00 45 3b c1
0x000001dc07dd3be3:   0f 83 17 08 00 00 43 8b 4c 83 10 49 8b 9f 50 04
0x000001dc07dd3bf3:   00 00 85 03 85 c9 0f 84 2e fa ff ff 4c 8b f1 49
0x000001dc07dd3c03:   8b fe 48 8b f5 48 3b fe 0f 84 b7 00 00 00 48 85
0x000001dc07dd3c13:   ff 0f 84 b5 00 00 00 48 85 f6 0f 84 ac 00 00 00
0x000001dc07dd3c23:   8b 4f 0c 3b 4e 0c 0f 85 a0 00 00 00 85 c9 0f 84
0x000001dc07dd3c33:   91 00 00 00 48 8d 7f 10 48 8d 76 10 d1 e1 8b c1
0x000001dc07dd3c43:   83 e0 1f 83 e1 e0 0f 84 4b 00 00 00 48 8d 3c 0f
0x000001dc07dd3c53:   48 8d 34 0e 48 f7 d9 c5 fe 6f 04 0f c5 fe 6f 0c
0x000001dc07dd3c63:   0e c5 fd ef c1 c4 e2 7d 17 c0 0f 85 5c 00 00 00
0x000001dc07dd3c73:   48 83 c1 20 75 e1 85 c0 0f 84 47 00 00 00 c5 fe
0x000001dc07dd3c83:   6f 44 07 e0 c5 fe 6f 4c 06 e0 c5 fd ef c1 c4 e2
0x000001dc07dd3c93:   7d 17 c0 75 37 eb 2e 8b c8 83 e1 fc 74 19 48 8d
0x000001dc07dd3ca3:   3c 0f 48 8d 34 0e 48 f7 d9 8b 1c 0f 3b 1c 0e 75
0x000001dc07dd3cb3:   1b 48 83 c1 04 75 f2 a8 02 74 0a 0f b7 1f 0f b7
0x000001dc07dd3cc3:   0e 3b d9 75 07 b8 01 00 00 00 eb 02 33 c0 c5 fd
0x000001dc07dd3cd3:   ef c0 c5 f5 ef c9 85 c0 75 16 41 ff c0 45 3b c1
0x000001dc07dd3ce3:   0f 85 f7 fe ff ff 33 c9 44 8b c1 e9 1b f9 ff ff
0x000001dc07dd3cf3:   4d 8b d6 e9 75 f9 ff ff 45 85 c0 0f 84 f3 00 00
0x000001dc07dd3d03:   00 45 0f be 58 10 41 3b cb 0f 85 e5 00 00 00 45
0x000001dc07dd3d13:   8b 50 14 44 8b 4f 14 45 8b 42 0c 41 8b 49 0c 41 


Stack slot to memory mapping:

stack at sp + 0 slots: 0x00000000814a89f8 is an oop: [C 
{0x00000000814a89f8} - klass: {type array char}
 - length: 9
stack at sp + 1 slots: 0x000000009e314738 is an oop: org.eclipse.jdt.internal.core.index.MemoryIndex 
{0x000000009e314738} - klass: 'org/eclipse/jdt/internal/core/index/MemoryIndex'
 - ---- fields (total size 4 words):
 - public 'NUM_CHANGES' 'I' @12  100 (0x00000064)
 - 'docsToReferences' 'Lorg/eclipse/jdt/internal/compiler/util/SimpleLookupTable;' @16  a 'org/eclipse/jdt/internal/compiler/util/SimpleLookupTable'{0x000000009e314758} (0x9e314758)
 - 'allWords' 'Lorg/eclipse/jdt/internal/core/util/SimpleWordSet;' @20  a 'org/eclipse/jdt/internal/core/util/SimpleWordSet'{0x000000009e318aa8} (0x9e318aa8)
 - 'lastDocumentName' 'Ljava/lang/String;' @24  "com/sun/xml/internal/bind/v2/schemagen/XmlSchemaGenerator$Namespace$3.class"{0x00000000d57dd340} (0xd57dd340)
 - 'lastReferenceTable' 'Lorg/eclipse/jdt/internal/compiler/util/HashtableOfObject;' @28  a 'org/eclipse/jdt/internal/compiler/util/HashtableOfObject'{0x00000000d57e3258} (0xd57e3258)
stack at sp + 2 slots: 0x00000000d57e4318 is an oop: org.eclipse.jdt.internal.core.util.SimpleWordSet 
{0x00000000d57e4318} - klass: 'org/eclipse/jdt/internal/core/util/SimpleWordSet'
 - ---- fields (total size 3 words):
 - public 'elementSize' 'I' @12  0 (0x00000000)
 - public 'threshold' 'I' @16  1 (0x00000001)
 - public 'words' '[[C' @20  a {type array char}[2] {0x00000000d57e4330} (0xd57e4330)
stack at sp + 3 slots: 0x00000000d57e4218 is an oop: [C 
{0x00000000d57e4218} - klass: {type array char}
 - length: 9
stack at sp + 4 slots: 0x0000000000000001 is an unknown value
stack at sp + 5 slots: 0x00000000d57e4318 is an oop: org.eclipse.jdt.internal.core.util.SimpleWordSet 
{0x00000000d57e4318} - klass: 'org/eclipse/jdt/internal/core/util/SimpleWordSet'
 - ---- fields (total size 3 words):
 - public 'elementSize' 'I' @12  0 (0x00000000)
 - public 'threshold' 'I' @16  1 (0x00000001)
 - public 'words' '[[C' @20  a {type array char}[2] {0x00000000d57e4330} (0xd57e4330)
stack at sp + 6 slots: 0x00000000d57dd3b8 is an oop: org.eclipse.jdt.internal.core.search.indexing.BinaryIndexer 
{0x00000000d57dd3b8} - klass: 'org/eclipse/jdt/internal/core/search/indexing/BinaryIndexer'
 - ---- fields (total size 2 words):
 - final 'document' 'Lorg/eclipse/jdt/core/search/SearchDocument;' @12  a 'org/eclipse/jdt/internal/core/search/JavaSearchDocument'{0x00000000d57dd1c0} (0xd57dd1c0)
stack at sp + 7 slots: 0x0000000081490838 is an oop: [C 
{0x0000000081490838} - klass: {type array char}
 - length: 4


Compiled method (c2) 190761 6786       4       org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry (118 bytes)
 total in heap  [0x000001dc07dd2c10,0x000001dc07dd5178] = 9576
 relocation     [0x000001dc07dd2d70,0x000001dc07dd2f38] = 456
 main code      [0x000001dc07dd2f40,0x000001dc07dd4810] = 6352
 stub code      [0x000001dc07dd4810,0x000001dc07dd4860] = 80
 oops           [0x000001dc07dd4860,0x000001dc07dd4870] = 16
 metadata       [0x000001dc07dd4870,0x000001dc07dd48e8] = 120
 scopes data    [0x000001dc07dd48e8,0x000001dc07dd4d18] = 1072
 scopes pcs     [0x000001dc07dd4d18,0x000001dc07dd5028] = 784
 dependencies   [0x000001dc07dd5028,0x000001dc07dd5040] = 24
 handler table  [0x000001dc07dd5040,0x000001dc07dd5130] = 240
 nul chk table  [0x000001dc07dd5130,0x000001dc07dd5178] = 72

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x000001dc5623ff80} 'addIndexEntry' '([C[CLjava/lang/String;)V' in 'org/eclipse/jdt/internal/core/index/MemoryIndex'
  # this:     rdx:rdx   = 'org/eclipse/jdt/internal/core/index/MemoryIndex'
  # parm0:    r8:r8     = '[C'
  # parm1:    r9:r9     = '[C'
  # parm2:    rdi:rdi   = 'java/lang/String'
  #           [sp+0x70]  (sp of caller)
  0x000001dc07dd2f40: 448b 5208 | 49bb 0000 | 0013 dc01 | 0000 4d03 | d349 3bc2 

  0x000001dc07dd2f54: ;   {runtime_call ic_miss_stub}
  0x000001dc07dd2f54: 0f85 26b7 | 7aff 6690 | 0f1f 4000 
[Verified Entry Point]
  0x000001dc07dd2f60: 8984 2400 | 80ff ff55 | 4883 ec60 | 4181 7f20 | 0400 0000 | 0f85 8918 | 0000 4c89 | 4c24 184c 
  0x000001dc07dd2f80: 8904 2448 | 897c 2410 | 4889 5424 | 0844 8b42 | 180f be4f | 104d 8bd0 | 493b fa0f | 855a 0d00 
  0x000001dc07dd2fa0: 0044 8b52 | 1c45 85d2 | 0f84 6213 | 0000 4d8b | da41 8b6b | 1444 8b55 | 0c4c 8b0c | 2441 8b51 
  0x000001dc07dd2fc0: 0c49 8bf9 | 4883 c710 | 83fa 020f | 8cf4 0f00 | 00bb 0100 | 0000 83fa | 200f 8c6b | 0100 0045 
  0x000001dc07dd2fe0: 33c0 c5d5 | efed c5cd | eff6 c5c5 | efff c441 

  0x000001dc07dd2ff0: ;   {external_word}
  0x000001dc07dd2ff0: 3def c048 | b8a0 616e | cff9 7f00 | 008b 08c5 | f96e c1c4 | e27d 58c0 | 8bc2 83e0 | e00f afd9 
  0x000001dc07dd3010: c421 7a6f | 0c47 c421 | 7a6f 5447 | 10c4 217a | 6f5c 4720 | c421 7a6f | 6447 30c4 | e255 40e8 
  0x000001dc07dd3030: c442 7d33 | c9c4 c155 | fee9 c4e2 | 4d40 f0c4 | 427d 33d2 | c4c1 4dfe | f2c4 e245 | 40f8 c442 
  0x000001dc07dd3050: 7d33 dbc4 | c145 fefb | c462 3d40 | c0c4 427d | 33e4 c441 | 3dfe c441 | 83c0 2044 | 3bc0 7c9d 
  0x000001dc07dd3070: 488d 3c47 

  0x000001dc07dd3074: ;   {external_word}
  0x000001dc07dd3074: 2bd0 48b8 | a461 6ecf | f97f 0000 | c5fe 6f08 | c4e2 5540 

  0x000001dc07dd3088: ;   {external_word}
  0x000001dc07dd3088: e948 b8c4 | 616e cff9 | 7f00 00c5 | fe6f 10c4 | e24d 40f2 

  0x000001dc07dd309c: ;   {external_word}
  0x000001dc07dd309c: 48b8 e461 | 6ecf f97f | 0000 c5fe | 6f18 c4e2 

  0x000001dc07dd30ac: ;   {external_word}
  0x000001dc07dd30ac: 4540 fb48 | b804 626e | cff9 7f00 | 00c5 fe6f | 20c4 623d | 40c4 c462 | 5502 cdc4 | 437d 39ca 
  0x000001dc07dd30cc: 01c4 4131 | feca c442 | 3102 c9c5 | 796e d3c4 | 4131 feca | c579 7ecb | c462 4d02 | dec4 437d 
  0x000001dc07dd30ec: 39dc 01c4 | 4121 fedc | c442 2102 | dbc5 796e | e3c4 4121 | fedc c579 | 7edb c462 | 4502 cfc4 
  0x000001dc07dd310c: 437d 39ca | 01c4 4131 | feca c442 | 3102 c9c5 | 796e d3c4 | 4131 feca | c579 7ecb | c442 3d02 
  0x000001dc07dd312c: d8c4 437d | 39dc 01c4 | 4121 fedc | c442 2102 | dbc5 796e | e3c4 4121 | fedc c579 | 7edb 41b8 
  0x000001dc07dd314c: 0100 0000 | 443b c20f | 8d27 0000 | 00b9 c103 | 0000 0faf | d942 0fb7 | 4447 fe8b | c8c1 e105 
  0x000001dc07dd316c: 2bc8 03d9 | 420f b70c | 4703 d941 | 83c0 0244 | 3bc2 7cd9 | 7f0f 8bc3 | c1e3 052b | d842 0fb7 
  0x000001dc07dd318c: 4c47 fe03 | d98b c325 | ffff ff7f | 4585 d20f | 868b 1100 | 003d 0000 | 0080 7508 | 33d2 4183 
  0x000001dc07dd31ac: ;   {no_reloc}
  0x000001dc07dd31ac: faff 7404 | 9941 f7fa | 8b4c 9510 | 85c9 0f84 | a40c 0000 | 488b f949 | 8bf1 483b | fe0f 84b7 
  0x000001dc07dd31cc: 0000 0048 | 85ff 0f84 | b500 0000 | 4885 f60f | 84ac 0000 | 008b 4f0c | 3b4e 0c0f | 85a0 0000 
  0x000001dc07dd31ec: 0085 c90f | 8491 0000 | 0048 8d7f | 1048 8d76 | 10d1 e18b | c183 e01f | 83e1 e00f | 844b 0000 
  0x000001dc07dd320c: 0048 8d3c | 0f48 8d34 | 0e48 f7d9 | c5fe 6f04 | 0fc5 fe6f | 0c0e c5fd | efc1 c4e2 | 7d17 c00f 
  0x000001dc07dd322c: 855c 0000 | 0048 83c1 | 2075 e185 | c00f 8447 | 0000 00c5 | fe6f 4407 | e0c5 fe6f | 4c06 e0c5 
  0x000001dc07dd324c: fdef c1c4 | e27d 17c0 | 7537 eb2e | 8bc8 83e1 | fc74 1948 | 8d3c 0f48 | 8d34 0e48 | f7d9 8b1c 
  0x000001dc07dd326c: 0f3b 1c0e | 751b 4883 | c104 75f2 | a802 740a | 0fb7 1f0f | b70e 3bd9 | 7507 b801 | 0000 00eb 
  0x000001dc07dd328c: 0233 c0c5 | fdef c0c5 | f5ef c985 | c00f 8521 | 0100 00ff | c241 3bd2 | 0f84 9012 | 0000 41bd 
  0x000001dc07dd32ac: ;   {no_reloc}
  0x000001dc07dd32ac: 0100 0000 | 413b d20f | 83a3 1100 | 008b 4c95 | 1049 8b9f | 5004 0000 | 4c8b 4424 

  0x000001dc07dd32c8: ; ImmutableOopMap {r11=Oop r8=Oop r9=Oop rcx=NarrowOop rbp=NarrowOop [8]=Oop [24]=Oop }
                      ;*ifnonnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.jdt.internal.compiler.util.HashtableOfObject::get@51 (line 86)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@72 (line 62)
                      ;   {poll}
  0x000001dc07dd32c8: 0885 0385 | c90f 8497 | 0b00 0048 | 8bf9 498b | f148 3bfe | 0f84 b700 | 0000 4885 | ff0f 84b5 
  0x000001dc07dd32e8: 0000 0048 | 85f6 0f84 | ac00 0000 | 8b4f 0c3b | 4e0c 0f85 | a000 0000 | 85c9 0f84 | 9100 0000 
  0x000001dc07dd3308: 488d 7f10 | 488d 7610 | d1e1 8bc1 | 83e0 1f83 | e1e0 0f84 | 4b00 0000 | 488d 3c0f | 488d 340e 
  0x000001dc07dd3328: 48f7 d9c5 | fe6f 0c0f | c5fe 6f04 | 0ec5 f5ef | c8c4 e27d | 17c9 0f85 | 5c00 0000 | 4883 c120 
  0x000001dc07dd3348: 75e1 85c0 | 0f84 4700 | 0000 c5fe | 6f4c 07e0 | c5fe 6f44 | 06e0 c5f5 | efc8 c4e2 | 7d17 c975 
  0x000001dc07dd3368: 37eb 2e8b | c883 e1fc | 7419 488d | 3c0f 488d | 340e 48f7 | d98b 1c0f | 3b1c 0e75 | 1b48 83c1 
  0x000001dc07dd3388: 0475 f2a8 | 0274 0a0f | b71f 0fb7 | 0e3b d975 | 07b8 0100 | 0000 eb02 | 33c0 c5f5 | efc9 c5fd 
  0x000001dc07dd33a8: efc0 85c0 | 7518 ffc2 | 413b d20f | 85f7 feff | ff8b eae9 | 7c11 0000 | 41bd 0100 | 0000 458b 
  0x000001dc07dd33c8: ;   {no_reloc}
  0x000001dc07dd33c8: 5318 458b | 420c 413b | d00f 839d | 0f00 0045 | 8b44 9210 | 4585 c00f | 8485 0a00 | 0041 8b48 
  0x000001dc07dd33e8: ;   {metadata('org/eclipse/jdt/internal/core/util/SimpleWordSet')}
  0x000001dc07dd33e8: 0881 f9a8 | 0446 010f | 859b 1200 | 004d 8bd0 | 4c89 5424 | 104c 8b54 | 2408 458b | 5214 458b 
  0x000001dc07dd3408: 5a14 458b | 4b0c 488b | 6c24 188b | 550c 488b | fd48 83c7 | 1083 fa02 | 0f8c bb0b | 0000 bb01 
  0x000001dc07dd3428: 0000 0083 | fa20 0f8c | 6701 0000 | 33f6 c5d5 | efed c5cd | eff6 c5c5 | efff c441 

  0x000001dc07dd3444: ;   {external_word}
  0x000001dc07dd3444: 3def c048 | b9a0 616e | cff9 7f00 | 0044 8b01 | c4c1 796e | c0c4 e27d | 58c0 8bca | 83e1 e041 
  0x000001dc07dd3464: 0faf d8c5 | 7a6f 0c77 | c57a 6f54 | 7710 c57a | 6f5c 7720 | c57a 6f64 | 7730 c4e2 | 5540 e8c4 
  0x000001dc07dd3484: 427d 33c9 | c4c1 55fe | e9c4 e24d | 40f0 c442 | 7d33 d2c4 | c14d fef2 | c4e2 4540 | f8c4 427d 
  0x000001dc07dd34a4: 33db c4c1 | 45fe fbc4 | 623d 40c0 | c442 7d33 | e4c4 413d | fec4 83c6 | 203b f17c | a248 8d3c 
  0x000001dc07dd34c4: ;   {external_word}
  0x000001dc07dd34c4: 4f2b d148 | b9a4 616e | cff9 7f00 | 00c5 fe6f | 09c4 e255 

  0x000001dc07dd34d8: ;   {external_word}
  0x000001dc07dd34d8: 40e9 48b9 | c461 6ecf | f97f 0000 | c5fe 6f11 | c4e2 4d40 

  0x000001dc07dd34ec: ;   {external_word}
  0x000001dc07dd34ec: f248 b9e4 | 616e cff9 | 7f00 00c5 | fe6f 19c4 | e245 40fb 

  0x000001dc07dd3500: ;   {external_word}
  0x000001dc07dd3500: 48b9 0462 | 6ecf f97f | 0000 c5fe | 6f21 c462 | 3d40 c4c4 | 6255 02cd | c443 7d39 | ca01 c441 
  0x000001dc07dd3520: 31fe cac4 | 4231 02c9 | c579 6ed3 | c441 31fe | cac5 797e | cbc4 624d | 02de c443 | 7d39 dc01 
  0x000001dc07dd3540: c441 21fe | dcc4 4221 | 02db c579 | 6ee3 c441 | 21fe dcc5 | 797e dbc4 | 6245 02cf | c443 7d39 
  0x000001dc07dd3560: ca01 c441 | 31fe cac4 | 4231 02c9 | c579 6ed3 | c441 31fe | cac5 797e | cbc4 423d | 02d8 c443 
  0x000001dc07dd3580: 7d39 dc01 | c441 21fe | dcc4 4221 | 02db c579 | 6ee3 c441 | 21fe dcc5 | 797e dbbe | 0100 0000 
  0x000001dc07dd35a0: 3bf2 0f8d | 2b00 0000 | 41b8 c103 | 0000 410f | afd8 0fb7 | 4c77 fe44 | 8bc1 41c1 | e005 442b 
  0x000001dc07dd35c0: c141 03d8 | 440f b704 | 7741 03d8 | 83c6 023b | f27c d57f | 108b cbc1 | e305 2bd9 | 440f b744 
  0x000001dc07dd35e0: 77fe 4103 | d88b c325 | ffff ff7f | 4585 c90f | 864f 0d00 | 003d 0000 | 0080 7508 

  0x000001dc07dd35fc: ;   {no_reloc}
  0x000001dc07dd35fc: 33d2 4183 | f9ff 7404 | 9941 f7f9 | 448b c249 | 8bd2 453b | c10f 839d | 0c00 0043 | 8b5c 8310 
  0x000001dc07dd361c: 498b 8f50 

  0x000001dc07dd3620: ; ImmutableOopMap {r10=NarrowOop r11=NarrowOop rbx=NarrowOop rdx=Oop rbp=Oop [16]=Oop }
                      ;*ifnonnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.jdt.internal.core.util.SimpleWordSet::add@47 (line 38)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@110 (line 66)
                      ;   {poll}
  0x000001dc07dd3620: 0400 0085 | 0185 db0f | 85c5 0400 | 0045 3bc1 | 0f83 760d | 0000 458b | 4a10 418b | 5a0c 488b 
  0x000001dc07dd3640: cdff c341 | 895a 0c4d | 8bd3 4f8d | 5483 1041 | 890a 49c1 | ea09 49bb | 0000 ad79 | dc01 0000 
  0x000001dc07dd3660: 4788 2413 | 413b d90f | 8f1c 0900 | 004c 8bd5 | 4c8b 7424 | 1041 8b6e | 1444 8b4d | 0c49 8bfa 
  0x000001dc07dd3680: 4883 c710 | 4d8b c241 | 8b52 0c83 | fa02 0f8c | 6909 0000 | 418b dd83 | fa20 0f8c | 6e01 0000 
  0x000001dc07dd36a0: 4533 dbc5 | d5ef edc5 | cdef f6c5 | c5ef ffc4 | 413d efc0 

  0x000001dc07dd36b4: ;   {external_word}
  0x000001dc07dd36b4: 48be a061 | 6ecf f97f | 0000 448b | 16c4 c179 | 6ec2 c4e2 | 7d58 c08b | f283 e6e0 | 410f afda 
  0x000001dc07dd36d4: c421 7a6f | 0c5f c421 | 7a6f 545f | 10c4 217a | 6f5c 5f20 | c421 7a6f | 645f 30c4 | e255 40e8 
  0x000001dc07dd36f4: c442 7d33 | c9c4 c155 | fee9 c4e2 | 4d40 f0c4 | 427d 33d2 | c4c1 4dfe | f2c4 e245 | 40f8 c442 
  0x000001dc07dd3714: 7d33 dbc4 | c145 fefb | c462 3d40 | c0c4 427d | 33e4 c441 | 3dfe c441 | 83c3 2044 | 3bde 7c9c 
  0x000001dc07dd3734: 488d 3c77 

  0x000001dc07dd3738: ;   {external_word}
  0x000001dc07dd3738: 2bd6 48be | a461 6ecf | f97f 0000 | c5fe 6f0e | c4e2 5540 

  0x000001dc07dd374c: ;   {external_word}
  0x000001dc07dd374c: e948 bec4 | 616e cff9 | 7f00 00c5 | fe6f 16c4 | e24d 40f2 

  0x000001dc07dd3760: ;   {external_word}
  0x000001dc07dd3760: 48be e461 | 6ecf f97f | 0000 c5fe | 6f1e c4e2 

  0x000001dc07dd3770: ;   {external_word}
  0x000001dc07dd3770: 4540 fb48 | be04 626e | cff9 7f00 | 00c5 fe6f | 26c4 623d | 40c4 c462 | 5502 cdc4 | 437d 39ca 
  0x000001dc07dd3790: 01c4 4131 | feca c442 | 3102 c9c5 | 796e d3c4 | 4131 feca | c579 7ecb | c462 4d02 | dec4 437d 
  0x000001dc07dd37b0: 39dc 01c4 | 4121 fedc | c442 2102 | dbc5 796e | e3c4 4121 | fedc c579 | 7edb c462 | 4502 cfc4 
  0x000001dc07dd37d0: 437d 39ca | 01c4 4131 | feca c442 | 3102 c9c5 | 796e d3c4 | 4131 feca | c579 7ecb | c442 3d02 
  0x000001dc07dd37f0: d8c4 437d | 39dc 01c4 | 4121 fedc | c442 2102 | dbc5 796e | e3c4 4121 | fedc c579 | 7edb 41bb 
  0x000001dc07dd3810: 0100 0000 | 443b da0f | 8d2e 0000 | 0041 bac1 | 0300 0041 | 0faf da42 | 0fb7 745f | fe44 8bd6 
  0x000001dc07dd3830: 41c1 e205 | 442b d641 | 03da 460f | b714 5f41 | 03da 4183 | c302 443b | da7c d27f | 108b f3c1 
  0x000001dc07dd3850: e305 2bde | 460f b754 | 5ffe 4103 | da8b c325 | ffff ff7f | 4585 c90f | 86ef 0a00 | 003d 0000 
  0x000001dc07dd3870: ;   {no_reloc}
  0x000001dc07dd3870: 0080 7508 | 33d2 4183 | f9ff 7404 | 9941 f7f9 | 413b d10f | 835f 0a00 | 0044 8b54 | 9510 4d8b 
  0x000001dc07dd3890: 9f50 0400 

  0x000001dc07dd3894: ; ImmutableOopMap {r10=NarrowOop r8=Oop rbp=NarrowOop r14=Oop }
                      ;*ifnonnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.jdt.internal.core.util.SimpleWordSet::add@47 (line 38)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@113 (line 66)
                      ;   {poll}
  0x000001dc07dd3894: 0041 8503 | 4585 d275 | 5641 3bd1 | 0f83 360b | 0000 418b | 4e0c 458b | 4e10 4d8b | d8ff c141 
  0x000001dc07dd38b4: 894e 0c4c | 8bd5 4c8d | 5495 1045 | 891a 49c1 | ea09 49bb | 0000 ad79 | dc01 0000 | 4788 2413 
  0x000001dc07dd38d4: 413b c90f | 8fcc 0600 | 00c5 f877 | 4883 c460 

  0x000001dc07dd38e4: ;   {poll_return}
  0x000001dc07dd38e4: 5d49 3ba7 | 4804 0000 | 0f87 fb0e | 0000 c349 | 8bfa 498b | f048 3bfe | 0f84 b700 | 0000 4885 
  0x000001dc07dd3904: ff0f 84b5 | 0000 0048 | 85f6 0f84 | ac00 0000 | 8b4f 0c3b | 4e0c 0f85 | a000 0000 | 85c9 0f84 
  0x000001dc07dd3924: 9100 0000 | 488d 7f10 | 488d 7610 | d1e1 8bc1 | 83e0 1f83 | e1e0 0f84 | 4b00 0000 | 488d 3c0f 
  0x000001dc07dd3944: 488d 340e | 48f7 d9c5 | fe6f 040f | c5fe 6f0c | 0ec5 fdef | c1c4 e27d | 17c0 0f85 | 5c00 0000 
  0x000001dc07dd3964: 4883 c120 | 75e1 85c0 | 0f84 4700 | 0000 c5fe | 6f44 07e0 | c5fe 6f4c | 06e0 c5fd | efc1 c4e2 
  0x000001dc07dd3984: 7d17 c075 | 37eb 2e8b | c883 e1fc | 7419 488d | 3c0f 488d | 340e 48f7 | d98b 1c0f | 3b1c 0e75 
  0x000001dc07dd39a4: 1b48 83c1 | 0475 f2a8 | 0274 0a0f | b71f 0fb7 | 0e3b d975 | 07b8 0100 | 0000 eb02 | 33c0 c5fd 
  0x000001dc07dd39c4: efc0 c5f5 | efc9 85c0 | 0f85 0bff | ffff ffc2 | 413b d10f | 840e 0100 | 0066 6690 | 413b d10f 
  0x000001dc07dd39e4: ;   {no_reloc}
  0x000001dc07dd39e4: 834b 0a00 | 0044 8b5c | 9510 4d8b | 9750 0400 

  0x000001dc07dd39f4: ; ImmutableOopMap {r11=NarrowOop r8=Oop rbp=NarrowOop r14=Oop }
                      ;*ifnonnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.jdt.internal.core.util.SimpleWordSet::add@47 (line 38)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@113 (line 66)
                      ;   {poll}
  0x000001dc07dd39f4: 0041 8502 | 4585 db0f | 849c feff | ff49 8bfb | 498b f048 | 3bfe 0f84 | b700 0000 | 4885 ff0f 
  0x000001dc07dd3a14: 84b5 0000 | 0048 85f6 | 0f84 ac00 | 0000 8b4f | 0c3b 4e0c | 0f85 a000 | 0000 85c9 | 0f84 9100 
  0x000001dc07dd3a34: 0000 488d | 7f10 488d | 7610 d1e1 | 8bc1 83e0 | 1f83 e1e0 | 0f84 4b00 | 0000 488d | 3c0f 488d 
  0x000001dc07dd3a54: 340e 48f7 | d9c5 fe6f | 040f c5fe | 6f0c 0ec5 | fdef c1c4 | e27d 17c0 | 0f85 5c00 | 0000 4883 
  0x000001dc07dd3a74: c120 75e1 | 85c0 0f84 | 4700 0000 | c5fe 6f44 | 07e0 c5fe | 6f4c 06e0 | c5fd efc1 | c4e2 7d17 
  0x000001dc07dd3a94: c075 37eb | 2e8b c883 | e1fc 7419 | 488d 3c0f | 488d 340e | 48f7 d98b | 1c0f 3b1c | 0e75 1b48 
  0x000001dc07dd3ab4: 83c1 0475 | f2a8 0274 | 0a0f b71f | 0fb7 0e3b | d975 07b8 | 0100 0000 | eb02 33c0 | c5fd efc0 
  0x000001dc07dd3ad4: c5f5 efc9 | 85c0 0f85 | fdfd ffff | ffc2 413b | d10f 85f5 | feff ff33 | d2e9 8efd | ffff 4c8b 
  0x000001dc07dd3af4: ;   {no_reloc}
  0x000001dc07dd3af4: f349 8bfe | 488b f548 | 3bfe 0f84 | b700 0000 | 4885 ff0f | 84b5 0000 | 0048 85f6 | 0f84 ac00 
  0x000001dc07dd3b14: 0000 8b4f | 0c3b 4e0c | 0f85 a000 | 0000 85c9 | 0f84 9100 | 0000 488d | 7f10 488d | 7610 d1e1 
  0x000001dc07dd3b34: 8bc1 83e0 | 1f83 e1e0 | 0f84 4b00 | 0000 488d | 3c0f 488d | 340e 48f7 | d9c5 fe6f | 040f c5fe 
  0x000001dc07dd3b54: 6f0c 0ec5 | fdef c1c4 | e27d 17c0 | 0f85 5c00 | 0000 4883 | c120 75e1 | 85c0 0f84 | 4700 0000 
  0x000001dc07dd3b74: c5fe 6f44 | 07e0 c5fe | 6f4c 06e0 | c5fd efc1 | c4e2 7d17 | c075 37eb | 2e8b c883 | e1fc 7419 
  0x000001dc07dd3b94: 488d 3c0f | 488d 340e | 48f7 d98b | 1c0f 3b1c | 0e75 1b48 | 83c1 0475 | f2a8 0274 | 0a0f b71f 
  0x000001dc07dd3bb4: 0fb7 0e3b | d975 07b8 | 0100 0000 | eb02 33c0 | c5fd efc0 | c5f5 efc9 | 85c0 0f85 | 1f01 0000 
  0x000001dc07dd3bd4: 41ff c045 | 3bc1 0f84 | 0901 0000 | 453b c10f | 8317 0800 | 0043 8b4c | 8310 498b 

  0x000001dc07dd3bf0: ;   {no_reloc}
  0x000001dc07dd3bf0: 9f50 0400 

  0x000001dc07dd3bf4: ; ImmutableOopMap {r10=NarrowOop r11=NarrowOop rcx=NarrowOop rdx=Oop rbp=Oop [16]=Oop }
                      ;*ifnonnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.jdt.internal.core.util.SimpleWordSet::add@47 (line 38)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@110 (line 66)
                      ;   {poll}
  0x000001dc07dd3bf4: 0085 0385 | c90f 842e | faff ff4c | 8bf1 498b | fe48 8bf5 | 483b fe0f | 84b7 0000 | 0048 85ff 
  0x000001dc07dd3c14: 0f84 b500 | 0000 4885 | f60f 84ac | 0000 008b | 4f0c 3b4e | 0c0f 85a0 | 0000 0085 | c90f 8491 
  0x000001dc07dd3c34: 0000 0048 | 8d7f 1048 | 8d76 10d1 | e18b c183 | e01f 83e1 | e00f 844b | 0000 0048 | 8d3c 0f48 
  0x000001dc07dd3c54: 8d34 0e48 | f7d9 c5fe | 6f04 0fc5 | fe6f 0c0e | c5fd efc1 | c4e2 7d17 | c00f 855c | 0000 0048 
  0x000001dc07dd3c74: 83c1 2075 | e185 c00f | 8447 0000 | 00c5 fe6f | 4407 e0c5 | fe6f 4c06 | e0c5 fdef | c1c4 e27d 
  0x000001dc07dd3c94: 17c0 7537 | eb2e 8bc8 | 83e1 fc74 | 1948 8d3c | 0f48 8d34 | 0e48 f7d9 | 8b1c 0f3b | 1c0e 751b 
  0x000001dc07dd3cb4: 4883 c104 | 75f2 a802 | 740a 0fb7 | 1f0f b70e | 3bd9 7507 | b801 0000 | 00eb 0233 | c0c5 fdef 
  0x000001dc07dd3cd4: c0c5 f5ef | c985 c075 | 1641 ffc0 | 453b c10f | 85f7 feff | ff33 c944 | 8bc1 e91b | f9ff ff4d 
  0x000001dc07dd3cf4: ;   {no_reloc}
  0x000001dc07dd3cf4: 8bd6 e975 | f9ff ff45 | 85c0 0f84 | f300 0000 | 450f be58 | 1041 3bcb | 0f85 e500 | 0000 458b 
  0x000001dc07dd3d14: 5014 448b | 4f14 458b | 420c 418b | 490c 413b | c80f 85cc | 0000 004d | 8bda 498d | 7210 4d8b 
  0x000001dc07dd3d34: d149 8d79 | 1085 c90f | 849d 0000 | 008b c183 | e01f 83e1 | e00f 844b | 0000 0048 | 8d3c 0f48 
  0x000001dc07dd3d54: 8d34 0e48 | f7d9 c5fe | 6f04 0fc5 | fe6f 0c0e | c5fd efc1 | c4e2 7d17 | c00f 8572 | 0000 0048 
  0x000001dc07dd3d74: 83c1 2075 | e185 c00f | 845d 0000 | 00c5 fe6f | 4407 e0c5 | fe6f 4c06 | e0c5 fdef | c1c4 e27d 
  0x000001dc07dd3d94: 17c0 754d | eb44 8bc8 | 83e1 fc74 | 1948 8d3c | 0f48 8d34 | 0e48 f7d9 | 8b1c 0f3b | 1c0e 7531 
  0x000001dc07dd3db4: 4883 c104 | 75f2 a802 | 7412 0fb7 | 1f0f b70e | 3bd9 751d | 488d 7f02 | 488d 7602 | a801 740a 
  0x000001dc07dd3dd4: 0fb6 1f0f | b60e 3bd9 | 7507 b801 | 0000 00eb | 0233 c0c5 | fdef c0c5 | f5ef c985 

  0x000001dc07dd3df0: ;   {no_reloc}
  0x000001dc07dd3df0: c00f 85aa | f1ff ff44 | 8b52 1045 | 85d2 0f84 | 7d04 0000 | 498b d24c | 8b44 2410 

  0x000001dc07dd3e0c: ;   {optimized virtual_call}
  0x000001dc07dd3e0c: c5f8 77e8 

  0x000001dc07dd3e10: ; ImmutableOopMap {[0]=Oop [8]=Oop [16]=Oop [24]=Oop }
                      ;*invokevirtual get {reexecute=0 rethrow=0 return_oop=1}
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@25 (line 55)
  0x000001dc07dd3e10: 4cf4 1800 

  0x000001dc07dd3e14: ;   {other}
  0x000001dc07dd3e14: 0f1f 8400 | 0412 0005 | 4885 c00f | 84f4 0100 | 0044 8b50 

  0x000001dc07dd3e28: ;   {metadata('org/eclipse/jdt/internal/compiler/util/HashtableOfObject')}
  0x000001dc07dd3e28: 0841 81fa | d84e 2f01 | 0f85 6604 | 0000 4c8b | d0ba 54ff | ffff 488b | 6c24 084c | 8b5c 2418 
  0x000001dc07dd3e48: 4c89 5c24 | 084c 8954 | 2420 6690 

  0x000001dc07dd3e54: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd3e54: c5f8 77e8 

  0x000001dc07dd3e58: ; ImmutableOopMap {rbp=Oop [0]=Oop [8]=Oop [16]=Oop [32]=Oop }
                      ;*ifnonnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@35 (line 56)
  0x000001dc07dd3e58: a42e 7bff 

  0x000001dc07dd3e5c: ;   {other}
  0x000001dc07dd3e5c: 0f1f 8400 | 4c12 0006 | 41bd 0100 | 0000 498b | 87b8 0100 | 004c 8bd0 | 4983 c218 | 4d3b 97c8 
  0x000001dc07dd3e7c: 0100 000f | 8377 0300 | 004d 8997 | b801 0000 | 410f 0d8a | c000 0000 | 48c7 0001 

  0x000001dc07dd3e98: ;   {metadata('org/eclipse/jdt/internal/core/util/SimpleWordSet')}
  0x000001dc07dd3e98: 0000 00c7 | 4008 a804 | 4601 4489 | 600c 48c7 | 4010 0100 | 0000 4c8b | c049 8b87 | b801 0000 
  0x000001dc07dd3eb8: 4c8b d049 | 83c2 184d | 3b97 c801 | 0000 0f83 | 7803 0000 | 4d89 97b8 | 0100 0041 | 0f0d 8ac0 
  0x000001dc07dd3ed8: 0000 0048 | c700 0100 | 0000 410f | 0d8a 0001 

  0x000001dc07dd3ee8: ;   {metadata({type array char}[])}
  0x000001dc07dd3ee8: 0000 c740 | 0830 0d04 | 0141 0f0d | 8a40 0100 | 00c7 400c | 0200 0000 | 410f 0d8a | 8001 0000 
  0x000001dc07dd3f08: 488b c848 | 83c1 10c5 | fdef c0c5 | f9d6 0148 | 8b6c 2408 | 4c89 0c24 | 4c89 5c24 | 1044 896c 
  0x000001dc07dd3f28: 2420 4c89 | 4424 284c | 8b44 2428 | 4d8b d04c | 8bd8 4589 | 5814 49c1 | ea09 49bb | 0000 ad79 
  0x000001dc07dd3f48: dc01 0000 | 4788 2413 | 488b 5424 | 104c 8b04 | 244c 8b4c | 2428 6690 

  0x000001dc07dd3f60: ;   {optimized virtual_call}
  0x000001dc07dd3f60: c5f8 77e8 

  0x000001dc07dd3f64: ; ImmutableOopMap {rbp=Oop [24]=Oop [40]=Oop }
                      ;*invokevirtual put {reexecute=0 rethrow=0 return_oop=1}
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@99 (line 64)
  0x000001dc07dd3f64: d8c4 e6ff 

  0x000001dc07dd3f68: ;   {other}
  0x000001dc07dd3f68: 0f1f 8400 | 5813 0007 | 4c8b 5424 | 284c 8954 | 2410 4889 | 6c24 0844 | 8b6c 2420 | e974 f4ff 
  0x000001dc07dd3f88: ff44 892c | 2466 6690 

  0x000001dc07dd3f90: ;   {optimized virtual_call}
  0x000001dc07dd3f90: c5f8 77e8 

  0x000001dc07dd3f94: ; ImmutableOopMap {rbp=Oop [16]=Oop }
                      ;*invokevirtual rehash {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.jdt.internal.core.util.SimpleWordSet::add@76 (line 45)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@110 (line 66)
  0x000001dc07dd3f94: c8e0 0300 

  0x000001dc07dd3f98: ;   {other}
  0x000001dc07dd3f98: 0f1f 8400 | 8813 0008 | 448b 2c24 | e9c4 f6ff | ff49 8be8 | 498b d690 

  0x000001dc07dd3fb0: ;   {optimized virtual_call}
  0x000001dc07dd3fb0: c5f8 77e8 

  0x000001dc07dd3fb4: ; ImmutableOopMap {rbp=Oop }
                      ;*invokevirtual rehash {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.jdt.internal.core.util.SimpleWordSet::add@76 (line 45)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@113 (line 66)
  0x000001dc07dd3fb4: a8e0 0300 

  0x000001dc07dd3fb8: ;   {other}
  0x000001dc07dd3fb8: 0f1f 8400 | a813 0009 | e918 f9ff | ff83 fa01 | 0f85 a605 | 0000 85d2 | 0f86 d205 | 0000 0fb7 
  0x000001dc07dd3fd8: 0783 c01f | e9b7 f1ff | ff83 fa01 | 0f85 ee05 | 0000 85d2 | 0f86 1206 | 0000 0fb7 | 0783 c01f 
  0x000001dc07dd3ff8: e9ef f5ff | ff83 fa01 | 0f85 2606 | 0000 85d2 | 0f86 4606 | 0000 0fb7 | 0783 c01f | e94b f8ff 
  0x000001dc07dd4018: ff4c 8b54 | 2408 458b | 5210 4489 | 5424 4049 | 8b87 b801 | 0000 4c8b | d049 83c2 | 204d 3b97 
  0x000001dc07dd4038: c801 0000 | 0f83 cd04 | 0000 4d89 | 97b8 0100 | 0041 0f0d | 8ac0 0000 | 0048 c700 | 0100 0000 
  0x000001dc07dd4058: 4c8b d049 

  0x000001dc07dd405c: ;   {metadata('org/eclipse/jdt/internal/compiler/util/HashtableOfObject')}
  0x000001dc07dd405c: 83c2 18c7 | 4008 d84e | 2f01 4489 | 600c c5fd | efc0 c4c1 | 79d6 0248 | c740 1003 | 0000 0048 
  0x000001dc07dd407c: 8be8 498b | 87b8 0100 | 004c 8bd0 | 4983 c228 | 4d3b 97c8 | 0100 000f | 833a 0400 | 004d 8997 
  0x000001dc07dd409c: b801 0000 | 410f 0d8a | c000 0000 | 48c7 0001 | 0000 0041 | 0f0d 8a00 

  0x000001dc07dd40b4: ;   {metadata({type array char}[])}
  0x000001dc07dd40b4: 0100 00c7 | 4008 300d | 0401 410f | 0d8a 4001 | 0000 c740 | 0c05 0000 | 0041 0f0d | 8a80 0100 
  0x000001dc07dd40d4: 004c 8bd8 | 4983 c310 | c5fd efc0 | 41ba 0700 | 0000 c4c1 | 7892 fa62 | d1fe 2f7f | 034c 8bdd 
  0x000001dc07dd40f4: 4c8b d044 | 8955 1449 | c1eb 0949 | ba00 00ad | 79dc 0100 | 0047 8824 | 1a49 8b87 | b801 0000 
  0x000001dc07dd4114: 4c8b d049 | 83c2 284d | 3b97 c801 | 0000 0f83 | 7003 0000 | 4d89 97b8 | 0100 0041 | 0f0d 8ac0 
  0x000001dc07dd4134: 0000 0048 | c700 0100 | 0000 410f | 0d8a 0001 

  0x000001dc07dd4144: ;   {metadata('java/lang/Object'[])}
  0x000001dc07dd4144: 0000 c740 | 0840 1200 | 0041 0f0d | 8a40 0100 | 00c7 400c | 0500 0000 | 410f 0d8a | 8001 0000 
  0x000001dc07dd4164: 4c8b d049 | 83c2 10c5 | fdef c041 | bb07 0000 | 00c4 c178 | 92fb 62d1 | fe2f 7f02 | 4c8b d54c 
  0x000001dc07dd4184: 8bd8 4489 | 5d18 49c1 | ea09 49bb | 0000 ad79 | dc01 0000 | 4788 2413 | 448b 5424 | 4045 85d2 
  0x000001dc07dd41a4: 0f84 ce04 | 0000 498b | d24c 8b44 | 2410 4c8b | cd66 6690 

  0x000001dc07dd41b8: ;   {optimized virtual_call}
  0x000001dc07dd41b8: c5f8 77e8 

  0x000001dc07dd41bc: ; ImmutableOopMap {rbp=Oop [0]=Oop [8]=Oop [16]=Oop [24]=Oop }
                      ;*invokevirtual put {reexecute=0 rethrow=0 return_oop=1}
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@54 (line 57)
  0x000001dc07dd41bc: e038 1000 

  0x000001dc07dd41c0: ;   {other}
  0x000001dc07dd41c0: 0f1f 8400 | b015 000a | 4c8b 5424 | 084c 8b5c | 2410 4c8b | 4424 0845 | 8958 1849 | c1ea 0949 
  0x000001dc07dd41e0: bb00 00ad | 79dc 0100 | 0047 8824 | 134c 8bdd | 4589 581c | 4c8b dde9 | b5ed ffff | 4489 6c24 
  0x000001dc07dd4200: 204c 895c | 2410 4c89 | 0c24 488b 

  0x000001dc07dd420c: ;   {metadata('org/eclipse/jdt/internal/core/util/SimpleWordSet')}
  0x000001dc07dd420c: 6c24 0848 | baa8 0446 | 14dc 0100 | 0066 6690 

  0x000001dc07dd421c: ;   {runtime_call _new_instance_Java}
  0x000001dc07dd421c: c5f8 77e8 

  0x000001dc07dd4220: ; ImmutableOopMap {rbp=Oop [0]=Oop [16]=Oop [24]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=1}
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@88 (line 64)
  0x000001dc07dd4220: dc8d 86ff 

  0x000001dc07dd4224: ;   {other}
  0x000001dc07dd4224: 0f1f 8400 | 1416 000b | 4889 6c24 | 084c 8b0c | 244c 8b5c | 2410 448b | 6c24 20e9 | 62fc ffff 
  0x000001dc07dd4244: 4c89 4424 | 2844 896c | 2420 4c89 | 5c24 104c | 890c 2448 | 8b6c 2408 

  0x000001dc07dd425c: ;   {metadata({type array char}[])}
  0x000001dc07dd425c: 48ba 300d | 0414 dc01 | 0000 41b8 | 0200 0000 

  0x000001dc07dd426c: ;   {runtime_call _new_array_Java}
  0x000001dc07dd426c: c5f8 77e8 

  0x000001dc07dd4270: ; ImmutableOopMap {rbp=Oop [0]=Oop [16]=Oop [24]=Oop [40]=Oop }
                      ;*anewarray {reexecute=0 rethrow=0 return_oop=1}
                      ; - org.eclipse.jdt.internal.core.util.SimpleWordSet::<init>@34 (line 31)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@93 (line 64)
  0x000001dc07dd4270: 8c90 86ff 

  0x000001dc07dd4274: ;   {other}
  0x000001dc07dd4274: 0f1f 8400 | 6416 000c | e9ae fcff | ffba f6ff | ffff 488b | 6c24 1090 

  0x000001dc07dd428c: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd428c: c5f8 77e8 

  0x000001dc07dd4290: ; ImmutableOopMap {rbp=Oop }
                      ;*invokevirtual get {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@25 (line 55)
  0x000001dc07dd4290: 6c2a 7bff 

  0x000001dc07dd4294: ;   {other}
  0x000001dc07dd4294: 0f1f 8400 | 8416 000d | bade ffff | ff48 8be8 

  0x000001dc07dd42a4: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd42a4: c5f8 77e8 

  0x000001dc07dd42a8: ; ImmutableOopMap {rbp=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@28 (line 55)
  0x000001dc07dd42a8: 542a 7bff 

  0x000001dc07dd42ac: ;   {other}
  0x000001dc07dd42ac: 0f1f 8400 | 9c16 000e | bae4 ffff | ff48 8b4c | 2410 4889 | 0c24 4489 | 5424 0844 | 894c 240c 
  0x000001dc07dd42cc: 4489 5c24 | 1444 8944 | 2418 6690 

  0x000001dc07dd42d8: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd42d8: c5f8 77e8 

  0x000001dc07dd42dc: ; ImmutableOopMap {rbp=Oop [0]=Oop [8]=NarrowOop [20]=NarrowOop }
                      ;*aaload {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.jdt.internal.core.util.SimpleWordSet::add@43 (line 38)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@110 (line 66)
  0x000001dc07dd42dc: 202a 7bff 

  0x000001dc07dd42e0: ;   {other}
  0x000001dc07dd42e0: 0f1f 8400 | d016 000f | 4c89 3424 | 4c89 4424 | 0844 894c | 2410 8954 | 2418 bae4 | ffff ff90 
  0x000001dc07dd4300: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd4300: c5f8 77e8 

  0x000001dc07dd4304: ; ImmutableOopMap {rbp=NarrowOop [0]=Oop [8]=Oop }
                      ;*aaload {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.jdt.internal.core.util.SimpleWordSet::add@43 (line 38)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@113 (line 66)
  0x000001dc07dd4304: f829 7bff 

  0x000001dc07dd4308: ;   {other}
  0x000001dc07dd4308: 0f1f 8400 | f816 0010 | baf6 ffff | ff48 8b2c | 2466 6690 

  0x000001dc07dd431c: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd431c: c5f8 77e8 

  0x000001dc07dd4320: ; ImmutableOopMap {rbp=Oop }
                      ;*invokevirtual get {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@72 (line 62)
  0x000001dc07dd4320: dc29 7bff 

  0x000001dc07dd4324: ;   {other}
  0x000001dc07dd4324: 0f1f 8400 | 1417 0011 | ba86 ffff | ff8b e890 

  0x000001dc07dd4334: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd4334: c5f8 77e8 

  0x000001dc07dd4338: ; ImmutableOopMap {}
                      ;*irem {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.jdt.internal.compiler.util.HashtableOfObject::get@11 (line 84)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@72 (line 62)
  0x000001dc07dd4338: c429 7bff 

  0x000001dc07dd433c: ;   {other}
  0x000001dc07dd433c: 0f1f 8400 | 2c17 0012 | ba86 ffff | ff8b e890 

  0x000001dc07dd434c: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd434c: c5f8 77e8 

  0x000001dc07dd4350: ; ImmutableOopMap {}
                      ;*irem {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.jdt.internal.core.util.SimpleWordSet::add@11 (line 36)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@110 (line 66)
  0x000001dc07dd4350: ac29 7bff 

  0x000001dc07dd4354: ;   {other}
  0x000001dc07dd4354: 0f1f 8400 | 4417 0013 | ba86 ffff | ff8b e890 

  0x000001dc07dd4364: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd4364: c5f8 77e8 

  0x000001dc07dd4368: ; ImmutableOopMap {}
                      ;*irem {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.jdt.internal.core.util.SimpleWordSet::add@11 (line 36)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@113 (line 66)
  0x000001dc07dd4368: 9429 7bff 

  0x000001dc07dd436c: ;   {other}
  0x000001dc07dd436c: 0f1f 8400 | 5c17 0014 | 488b 6c24 | 084c 890c | 244c 8b44 | 2418 4c89 | 4424 084c | 895c 2410 
  0x000001dc07dd438c: 4489 5424 | 1889 5424 | 1cba e4ff | ffff 6690 

  0x000001dc07dd439c: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd439c: c5f8 77e8 

  0x000001dc07dd43a0: ; ImmutableOopMap {rbp=Oop [0]=Oop [8]=Oop [16]=Oop [24]=NarrowOop }
                      ;*aaload {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.jdt.internal.compiler.util.HashtableOfObject::get@30 (line 88)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@72 (line 62)
  0x000001dc07dd43a0: 5c29 7bff 

  0x000001dc07dd43a4: ;   {other}
  0x000001dc07dd43a4: 0f1f 8400 | 9417 0015 | bae4 ffff | ff4c 8b4c | 2410 4c89 | 0c24 4489 | 5424 0844 | 895c 240c 
  0x000001dc07dd43c4: 4489 4424 | 1066 6690 

  0x000001dc07dd43cc: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd43cc: c5f8 77e8 

  0x000001dc07dd43d0: ; ImmutableOopMap {rbp=Oop [0]=Oop [8]=NarrowOop [12]=NarrowOop }
                      ;*aastore {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.jdt.internal.core.util.SimpleWordSet::add@56 (line 42)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@110 (line 66)
  0x000001dc07dd43d0: 2c29 7bff 

  0x000001dc07dd43d4: ;   {other}
  0x000001dc07dd43d4: 0f1f 8400 | c417 0016 | 4c89 3424 | 8954 2410 | 4c89 4424 | 18ba e4ff | ffff 6690 

  0x000001dc07dd43f0: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd43f0: c5f8 77e8 

  0x000001dc07dd43f4: ; ImmutableOopMap {rbp=NarrowOop [0]=Oop [24]=Oop }
                      ;*aastore {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.jdt.internal.core.util.SimpleWordSet::add@56 (line 42)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@113 (line 66)
  0x000001dc07dd43f4: 0829 7bff 

  0x000001dc07dd43f8: ;   {other}
  0x000001dc07dd43f8: 0f1f 8400 | e817 0017 | bae4 ffff | ff48 8b4c | 2410 4889 | 0c24 4489 | 5424 0844 | 894c 240c 
  0x000001dc07dd4418: 4489 5c24 | 1444 8944 | 2418 6690 

  0x000001dc07dd4424: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd4424: c5f8 77e8 

  0x000001dc07dd4428: ; ImmutableOopMap {rbp=Oop [0]=Oop [8]=NarrowOop [20]=NarrowOop }
                      ;*aaload {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.jdt.internal.core.util.SimpleWordSet::add@43 (line 38)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@110 (line 66)
  0x000001dc07dd4428: d428 7bff 

  0x000001dc07dd442c: ;   {other}
  0x000001dc07dd442c: 0f1f 8400 | 1c18 0018 | 4c89 3424 | 4c89 4424 | 0844 894c | 2410 8954 | 2418 bae4 | ffff ff90 
  0x000001dc07dd444c: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd444c: c5f8 77e8 

  0x000001dc07dd4450: ; ImmutableOopMap {rbp=NarrowOop [0]=Oop [8]=Oop }
                      ;*aaload {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.jdt.internal.core.util.SimpleWordSet::add@43 (line 38)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@113 (line 66)
  0x000001dc07dd4450: ac28 7bff 

  0x000001dc07dd4454: ;   {other}
  0x000001dc07dd4454: 0f1f 8400 | 4418 0019 | 4c8b 4424 | 084c 8904 | 244c 8b44 | 2418 4c89 | 4424 104c | 895c 2420 
  0x000001dc07dd4474: 4c89 4c24 | 2844 8954 | 2430 8954 | 2438 bae4 | ffff ff90 

  0x000001dc07dd4488: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd4488: c5f8 77e8 

  0x000001dc07dd448c: ; ImmutableOopMap {rbp=NarrowOop [0]=Oop [16]=Oop [32]=Oop [40]=Oop }
                      ;*aaload {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.jdt.internal.compiler.util.HashtableOfObject::get@47 (line 86)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@72 (line 62)
  0x000001dc07dd448c: 7028 7bff 

  0x000001dc07dd4490: ;   {other}
  0x000001dc07dd4490: 0f1f 8400 | 8018 001a | 4889 6c24 | 2048 8b6c 

  0x000001dc07dd44a0: ;   {metadata('java/lang/Object'[])}
  0x000001dc07dd44a0: 2410 48ba | 4012 0013 | dc01 0000 | 41b8 0500 | 0000 6690 

  0x000001dc07dd44b4: ;   {runtime_call _new_array_Java}
  0x000001dc07dd44b4: c5f8 77e8 

  0x000001dc07dd44b8: ; ImmutableOopMap {rbp=Oop [0]=Oop [8]=Oop [24]=Oop [32]=Oop [64]=NarrowOop }
                      ;*anewarray {reexecute=0 rethrow=0 return_oop=1}
                      ; - org.eclipse.jdt.internal.compiler.util.HashtableOfObject::<init>@71 (line 57)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@48 (line 57)
  0x000001dc07dd44b8: 448e 86ff 

  0x000001dc07dd44bc: ;   {other}
  0x000001dc07dd44bc: 0f1f 8400 | ac18 001b | 4889 6c24 | 1048 8b6c | 2420 e9ad | fcff ff48 | 896c 2420 | 488b 6c24 
  0x000001dc07dd44dc: ;   {metadata({type array char}[])}
  0x000001dc07dd44dc: 1048 ba30 | 0d04 14dc | 0100 0041 | b805 0000 | 0066 6690 

  0x000001dc07dd44f0: ;   {runtime_call _new_array_Java}
  0x000001dc07dd44f0: c5f8 77e8 

  0x000001dc07dd44f4: ; ImmutableOopMap {rbp=Oop [0]=Oop [8]=Oop [24]=Oop [32]=Oop [64]=NarrowOop }
                      ;*anewarray {reexecute=0 rethrow=0 return_oop=1}
                      ; - org.eclipse.jdt.internal.compiler.util.HashtableOfObject::<init>@63 (line 56)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@48 (line 57)
  0x000001dc07dd44f4: 088e 86ff 

  0x000001dc07dd44f8: ;   {other}
  0x000001dc07dd44f8: 0f1f 8400 | e818 001c | 4889 6c24 | 1048 8b6c | 2420 e9e2 | fbff ff48 | 8b6c 2410 

  0x000001dc07dd4514: ;   {metadata('org/eclipse/jdt/internal/compiler/util/HashtableOfObject')}
  0x000001dc07dd4514: 48ba d84e | 2f14 dc01 | 0000 6690 

  0x000001dc07dd4520: ;   {runtime_call _new_instance_Java}
  0x000001dc07dd4520: c5f8 77e8 

  0x000001dc07dd4524: ; ImmutableOopMap {rbp=Oop [0]=Oop [8]=Oop [24]=Oop [64]=NarrowOop }
                      ;*new {reexecute=0 rethrow=0 return_oop=1}
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@43 (line 57)
  0x000001dc07dd4524: d88a 86ff 

  0x000001dc07dd4528: ;   {other}
  0x000001dc07dd4528: 0f1f 8400 | 1819 001d | 4889 6c24 | 10e9 39fb | ffff 8bea | ba45 ffff | ff4c 8b44 | 2408 4c89 
  0x000001dc07dd4548: 0424 4c8b | 4424 184c | 8944 2410 | 4c89 5c24 | 204c 894c | 2428 4489 | 5424 3490 

  0x000001dc07dd4564: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd4564: c5f8 77e8 

  0x000001dc07dd4568: ; ImmutableOopMap {[0]=Oop [16]=Oop [32]=Oop [40]=Oop }
                      ;*if_icmpne {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.jdt.internal.compiler.util.HashtableOfObject::get@37 (line 90)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@72 (line 62)
  0x000001dc07dd4568: 9427 7bff 

  0x000001dc07dd456c: ;   {other}
  0x000001dc07dd456c: 0f1f 8400 | 5c19 001e | 488b 6c24 | 084c 8b44 | 2418 4c89 | 4424 084c | 895c 2418 | 4489 5424 
  0x000001dc07dd458c: 2889 5424 | 2cba 45ff | ffff 6690 

  0x000001dc07dd4598: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd4598: c5f8 77e8 

  0x000001dc07dd459c: ; ImmutableOopMap {rbp=Oop [0]=Oop [8]=Oop [24]=Oop }
                      ;*lookupswitch {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.Arrays::hashCode@8
                      ; - org.eclipse.jdt.core.compiler.CharOperation::hashCode@1 (line 2489)
                      ; - org.eclipse.jdt.internal.compiler.util.HashtableOfObject::get@7 (line 84)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@72 (line 62)
  0x000001dc07dd459c: 6027 7bff 

  0x000001dc07dd45a0: ;   {other}
  0x000001dc07dd45a0: 0f1f 8400 | 9019 001f | bae4 ffff | ff48 8b6c | 2408 4c8b | 4424 184c | 8944 2408 | 4c89 5c24 
  0x000001dc07dd45c0: 1844 8954 | 2428 6690 

  0x000001dc07dd45c8: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd45c8: c5f8 77e8 

  0x000001dc07dd45cc: ; ImmutableOopMap {rbp=Oop [0]=Oop [8]=Oop [24]=Oop }
                      ;*caload {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.Arrays::hashCode@44
                      ; - org.eclipse.jdt.core.compiler.CharOperation::hashCode@1 (line 2489)
                      ; - org.eclipse.jdt.internal.compiler.util.HashtableOfObject::get@7 (line 84)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@72 (line 62)
  0x000001dc07dd45cc: 3027 7bff 

  0x000001dc07dd45d0: ;   {other}
  0x000001dc07dd45d0: 0f1f 8400 | c019 0020 | 4c8b 5c24 | 104c 891c | 2444 8954 | 2408 4489 | 4c24 0c89 | 5424 18ba 
  0x000001dc07dd45f0: 45ff ffff 

  0x000001dc07dd45f4: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd45f4: c5f8 77e8 

  0x000001dc07dd45f8: ; ImmutableOopMap {rbp=Oop [0]=Oop [8]=NarrowOop }
                      ;*lookupswitch {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.Arrays::hashCode@8
                      ; - org.eclipse.jdt.core.compiler.CharOperation::hashCode@1 (line 2489)
                      ; - org.eclipse.jdt.internal.core.util.SimpleWordSet::add@7 (line 36)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@110 (line 66)
  0x000001dc07dd45f8: 0427 7bff 

  0x000001dc07dd45fc: ;   {other}
  0x000001dc07dd45fc: 0f1f 8400 | ec19 0021 | bae4 ffff | ff4c 8b5c | 2410 4c89 | 1c24 4489 | 5424 0844 | 894c 240c 
  0x000001dc07dd461c: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd461c: c5f8 77e8 

  0x000001dc07dd4620: ; ImmutableOopMap {rbp=Oop [0]=Oop [8]=NarrowOop }
                      ;*caload {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.Arrays::hashCode@44
                      ; - org.eclipse.jdt.core.compiler.CharOperation::hashCode@1 (line 2489)
                      ; - org.eclipse.jdt.internal.core.util.SimpleWordSet::add@7 (line 36)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@110 (line 66)
  0x000001dc07dd4620: dc26 7bff 

  0x000001dc07dd4624: ;   {other}
  0x000001dc07dd4624: 0f1f 8400 | 141a 0022 | 488b 6c24 | 1044 894c | 2408 4c89 | 5424 1089 | 5424 0cba | 45ff ffff 
  0x000001dc07dd4644: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd4644: c5f8 77e8 

  0x000001dc07dd4648: ; ImmutableOopMap {rbp=Oop [16]=Oop }
                      ;*lookupswitch {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.Arrays::hashCode@8
                      ; - org.eclipse.jdt.core.compiler.CharOperation::hashCode@1 (line 2489)
                      ; - org.eclipse.jdt.internal.core.util.SimpleWordSet::add@7 (line 36)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@113 (line 66)
  0x000001dc07dd4648: b426 7bff 

  0x000001dc07dd464c: ;   {other}
  0x000001dc07dd464c: 0f1f 8400 | 3c1a 0023 | bae4 ffff | ff48 8b6c | 2410 4489 | 4c24 084c | 8954 2410 

  0x000001dc07dd4668: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd4668: c5f8 77e8 

  0x000001dc07dd466c: ; ImmutableOopMap {rbp=Oop [16]=Oop }
                      ;*caload {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.util.Arrays::hashCode@44
                      ; - org.eclipse.jdt.core.compiler.CharOperation::hashCode@1 (line 2489)
                      ; - org.eclipse.jdt.internal.core.util.SimpleWordSet::add@7 (line 36)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@113 (line 66)
  0x000001dc07dd466c: 9026 7bff 

  0x000001dc07dd4670: ;   {other}
  0x000001dc07dd4670: 0f1f 8400 | 601a 0024 | baf6 ffff | ff66 6690 

  0x000001dc07dd4680: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd4680: c5f8 77e8 

  0x000001dc07dd4684: ; ImmutableOopMap {rbp=Oop [16]=Oop }
                      ;*invokevirtual put {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@54 (line 57)
  0x000001dc07dd4684: 7826 7bff 

  0x000001dc07dd4688: ;   {other}
  0x000001dc07dd4688: 0f1f 8400 | 781a 0025 | bade ffff | ff48 8b6c | 2408 4c89 | 0c24 4c8b | 5424 184c | 8954 2408 
  0x000001dc07dd46a8: 4c89 5c24 | 1044 8944 | 2418 6690 

  0x000001dc07dd46b4: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd46b4: c5f8 77e8 

  0x000001dc07dd46b8: ; ImmutableOopMap {rbp=Oop [0]=Oop [8]=Oop [16]=Oop [24]=NarrowOop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@75 (line 62)
  0x000001dc07dd46b8: 4426 7bff 

  0x000001dc07dd46bc: ;   {other}
  0x000001dc07dd46bc: 0f1f 8400 | ac1a 0026 | baf6 ffff | ff41 8be8 

  0x000001dc07dd46cc: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd46cc: c5f8 77e8 

  0x000001dc07dd46d0: ; ImmutableOopMap {rbp=NarrowOop }
                      ;*invokevirtual equals {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@5 (line 51)
  0x000001dc07dd46d0: 2c26 7bff 

  0x000001dc07dd46d4: ;   {other}
  0x000001dc07dd46d4: 0f1f 8400 | c41a 0027 | baf6 ffff | ff66 6690 

  0x000001dc07dd46e4: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd46e4: c5f8 77e8 

  0x000001dc07dd46e8: ; ImmutableOopMap {}
                      ;*arraylength {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.jdt.internal.compiler.util.HashtableOfObject::get@4 (line 83)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@72 (line 62)
  0x000001dc07dd46e8: 1426 7bff 

  0x000001dc07dd46ec: ;   {other}
  0x000001dc07dd46ec: 0f1f 8400 | dc1a 0028 | baf6 ffff | ff48 8b6c | 2418 6690 

  0x000001dc07dd4700: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd4700: c5f8 77e8 

  0x000001dc07dd4704: ; ImmutableOopMap {rbp=Oop }
                      ;*invokevirtual add {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@110 (line 66)
  0x000001dc07dd4704: f825 7bff 

  0x000001dc07dd4708: ;   {other}
  0x000001dc07dd4708: 0f1f 8400 | f81a 0029 | baf6 ffff | ff66 6690 

  0x000001dc07dd4718: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd4718: c5f8 77e8 

  0x000001dc07dd471c: ; ImmutableOopMap {}
                      ;*arraylength {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.jdt.internal.core.util.SimpleWordSet::add@4 (line 35)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@110 (line 66)
  0x000001dc07dd471c: e025 7bff 

  0x000001dc07dd4720: ;   {other}
  0x000001dc07dd4720: 0f1f 8400 | 101b 002a | baf6 ffff | ff66 6690 

  0x000001dc07dd4730: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd4730: c5f8 77e8 

  0x000001dc07dd4734: ; ImmutableOopMap {}
                      ;*arraylength {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.jdt.internal.core.util.SimpleWordSet::add@4 (line 35)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@113 (line 66)
  0x000001dc07dd4734: c825 7bff 

  0x000001dc07dd4738: ;   {other}
  0x000001dc07dd4738: 0f1f 8400 | 281b 002b | 8bea baf6 | ffff ff90 

  0x000001dc07dd4748: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd4748: c5f8 77e8 

  0x000001dc07dd474c: ; ImmutableOopMap {}
                      ;*aaload {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.jdt.internal.compiler.util.HashtableOfObject::get@30 (line 88)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@72 (line 62)
  0x000001dc07dd474c: b025 7bff 

  0x000001dc07dd4750: ;   {other}
  0x000001dc07dd4750: 0f1f 8400 | 401b 002c | ba45 ffff | ff48 8b6c | 2408 4c8b | 4424 184c | 8944 2408 | 4c89 5c24 
  0x000001dc07dd4770: 1844 8954 | 2428 6690 

  0x000001dc07dd4778: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd4778: c5f8 77e8 

  0x000001dc07dd477c: ; ImmutableOopMap {rbp=Oop [0]=Oop [8]=Oop [24]=Oop }
                      ;*ifnonnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.Arrays::hashCode@1
                      ; - org.eclipse.jdt.core.compiler.CharOperation::hashCode@1 (line 2489)
                      ; - org.eclipse.jdt.internal.compiler.util.HashtableOfObject::get@7 (line 84)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@72 (line 62)
  0x000001dc07dd477c: 8025 7bff 

  0x000001dc07dd4780: ;   {other}
  0x000001dc07dd4780: 0f1f 8400 | 701b 002d | ba45 ffff | ff4c 8b5c | 2410 4c89 | 1c24 4489 | 5424 0844 | 894c 240c 
  0x000001dc07dd47a0: ;   {runtime_call UncommonTrapBlob}
  0x000001dc07dd47a0: c5f8 77e8 

  0x000001dc07dd47a4: ; ImmutableOopMap {rbp=Oop [0]=Oop [8]=NarrowOop }
                      ;*ifnonnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.Arrays::hashCode@1
                      ; - org.eclipse.jdt.core.compiler.CharOperation::hashCode@1 (line 2489)
                      ; - org.eclipse.jdt.internal.core.util.SimpleWordSet::add@7 (line 36)
                      ; - org.eclipse.jdt.internal.core.index.MemoryIndex::addIndexEntry@110 (line 66)
  0x000001dc07dd47a4: 5825 7bff 

  0x000001dc07dd47a8: ;   {other}
  0x000001dc07dd47a8: 0f1f 8400 | 981b 002e | 488b d0eb | 2b48 8bd0 | eb26 488b | d0eb 2148 | 8bd0 eb1c | 488b d0eb 
  0x000001dc07dd47c8: 1748 8bd0 | eb12 488b | d0eb 0d48 | 8bd0 eb08 | 488b d0eb | 0348 8bd0 | c5f8 7748 | 83c4 605d 
  0x000001dc07dd47e8: ;   {runtime_call _rethrow_Java}
  0x000001dc07dd47e8: e993 c486 

  0x000001dc07dd47ec: ;   {internal_word}
  0x000001dc07dd47ec: ff49 bae5 | 38dd 07dc | 0100 004d | 8997 6004 

  0x000001dc07dd47fc: ;   {runtime_call SafepointBlob}
  0x000001dc07dd47fc: 0000 e97d 

  0x000001dc07dd4800: ;   {runtime_call StubRoutines (final stubs)}
  0x000001dc07dd4800: 137b ffe8 | f88f 79ff | e96d e7ff | fff4 f4f4 
[Stub Code]
  0x000001dc07dd4810: ;   {no_reloc}
  0x000001dc07dd4810: 48bb 0000 | 0000 0000 

  0x000001dc07dd4818: ;   {runtime_call nmethod}
  0x000001dc07dd4818: 0000 e9fb 

  0x000001dc07dd481c: ;   {static_stub}
  0x000001dc07dd481c: ffff ff48 | bb20 d325 | 56dc 0100 

  0x000001dc07dd4828: ;   {runtime_call I2C/C2I adapters}
  0x000001dc07dd4828: 00e9 3d22 

  0x000001dc07dd482c: ;   {static_stub}
  0x000001dc07dd482c: 7bff 48bb | 0000 0000 | 0000 0000 

  0x000001dc07dd4838: ;   {runtime_call nmethod}
  0x000001dc07dd4838: e9fb ffff 

  0x000001dc07dd483c: ;   {static_stub}
  0x000001dc07dd483c: ff48 bb98 | d324 56dc 

  0x000001dc07dd4844: ;   {runtime_call I2C/C2I adapters}
  0x000001dc07dd4844: 0100 00e9 | 9793 7aff 
[Exception Handler]
  0x000001dc07dd484c: ;   {runtime_call ExceptionBlob}
  0x000001dc07dd484c: e9af 4b86 | ffe8 0000 | 0000 4883 

  0x000001dc07dd4858: ;   {runtime_call DeoptimizationBlob}
  0x000001dc07dd4858: 2c24 05e9 | c027 7bff 
[/MachCode]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001dc5d02cd10, length=58, elements={
0x000001dc7818a480, 0x000001dc7a49f0f0, 0x000001dc7a4a2220, 0x000001dc7a4a7790,
0x000001dc7a4a8790, 0x000001dc7a4ae580, 0x000001dc7a4b1780, 0x000001dc7a4c0d30,
0x000001dc7a4c9b10, 0x000001dc7f82f140, 0x000001dc7f82f7d0, 0x000001dc7f82fe60,
0x000001dc587b4f50, 0x000001dc587b3ba0, 0x000001dc587b4230, 0x000001dc587b48c0,
0x000001dc587b6300, 0x000001dc587b2e80, 0x000001dc587b6990, 0x000001dc587b3510,
0x000001dc587b7020, 0x000001dc587b9e10, 0x000001dc587b90f0, 0x000001dc587b9780,
0x000001dc587b76b0, 0x000001dc587b8a60, 0x000001dc587b7d40, 0x000001dc587b83d0,
0x000001dc5a211570, 0x000001dc5a211c00, 0x000001dc5a212290, 0x000001dc5a212920,
0x000001dc5a212fb0, 0x000001dc5a213640, 0x000001dc5a2101c0, 0x000001dc5a213cd0,
0x000001dc5a214360, 0x000001dc5a210850, 0x000001dc5a216ac0, 0x000001dc5a216430,
0x000001dc5a2177e0, 0x000001dc5a215710, 0x000001dc5a215da0, 0x000001dc7f82eab0,
0x000001dc7f8304f0, 0x000001dc7f82d070, 0x000001dc7f82d700, 0x000001dc7f82dd90,
0x000001dc7f82e420, 0x000001dc587b55e0, 0x000001dc5aa53120, 0x000001dc5aa57950,
0x000001dc5aa59390, 0x000001dc5aa55f10, 0x000001dc5aa57fe0, 0x000001dc5aa56c30,
0x000001dc5aababc0, 0x000001dc5aabcc90
}

Java Threads: ( => current thread )
  0x000001dc7818a480 JavaThread "main"                              [_thread_blocked, id=20664, stack(0x000000d9acc00000,0x000000d9acd00000) (1024K)]
  0x000001dc7a49f0f0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=3148, stack(0x000000d9ad000000,0x000000d9ad100000) (1024K)]
  0x000001dc7a4a2220 JavaThread "Finalizer"                  daemon [_thread_blocked, id=19500, stack(0x000000d9ad100000,0x000000d9ad200000) (1024K)]
  0x000001dc7a4a7790 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=23056, stack(0x000000d9ad200000,0x000000d9ad300000) (1024K)]
  0x000001dc7a4a8790 JavaThread "Attach Listener"            daemon [_thread_blocked, id=12280, stack(0x000000d9ad300000,0x000000d9ad400000) (1024K)]
  0x000001dc7a4ae580 JavaThread "Service Thread"             daemon [_thread_blocked, id=12688, stack(0x000000d9ad400000,0x000000d9ad500000) (1024K)]
  0x000001dc7a4b1780 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=15500, stack(0x000000d9ad500000,0x000000d9ad600000) (1024K)]
  0x000001dc7a4c0d30 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=26932, stack(0x000000d9ad600000,0x000000d9ad700000) (1024K)]
  0x000001dc7a4c9b10 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=21316, stack(0x000000d9ad700000,0x000000d9ad800000) (1024K)]
  0x000001dc7f82f140 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=10416, stack(0x000000d9ad800000,0x000000d9ad900000) (1024K)]
  0x000001dc7f82f7d0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=1868, stack(0x000000d9ada00000,0x000000d9adb00000) (1024K)]
  0x000001dc7f82fe60 JavaThread "Active Thread: Equinox Container: b18ee8e7-a3a6-4a42-90e4-82f22d74a76b"        [_thread_blocked, id=17404, stack(0x000000d9ae800000,0x000000d9ae900000) (1024K)]
  0x000001dc587b4f50 JavaThread "Framework Event Dispatcher: Equinox Container: b18ee8e7-a3a6-4a42-90e4-82f22d74a76b" daemon [_thread_blocked, id=3224, stack(0x000000d9adb00000,0x000000d9adc00000) (1024K)]
  0x000001dc587b3ba0 JavaThread "Start Level: Equinox Container: b18ee8e7-a3a6-4a42-90e4-82f22d74a76b" daemon [_thread_blocked, id=14412, stack(0x000000d9ae900000,0x000000d9aea00000) (1024K)]
  0x000001dc587b4230 JavaThread "Refresh Thread: Equinox Container: b18ee8e7-a3a6-4a42-90e4-82f22d74a76b" daemon [_thread_blocked, id=24968, stack(0x000000d9aeb00000,0x000000d9aec00000) (1024K)]
  0x000001dc587b48c0 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=25508, stack(0x000000d9aef00000,0x000000d9af000000) (1024K)]
  0x000001dc587b6300 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=22364, stack(0x000000d9ad900000,0x000000d9ada00000) (1024K)]
  0x000001dc587b2e80 JavaThread "Worker-JM"                         [_thread_blocked, id=13816, stack(0x000000d9af100000,0x000000d9af200000) (1024K)]
  0x000001dc587b6990 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=13152, stack(0x000000d9af200000,0x000000d9af300000) (1024K)]
  0x000001dc587b3510 JavaThread "Worker-0"                          [_thread_blocked, id=15384, stack(0x000000d9af300000,0x000000d9af400000) (1024K)]
  0x000001dc587b7020 JavaThread "Worker-1: Java indexing... "        [_thread_blocked, id=24700, stack(0x000000d9af400000,0x000000d9af500000) (1024K)]
=>0x000001dc587b9e10 JavaThread "Java indexing"              daemon [_thread_in_Java, id=9364, stack(0x000000d9af600000,0x000000d9af700000) (1024K)]
  0x000001dc587b90f0 JavaThread "Worker-2"                          [_thread_blocked, id=20060, stack(0x000000d9af900000,0x000000d9afa00000) (1024K)]
  0x000001dc587b9780 JavaThread "Worker-3: Initialize workspace"        [_thread_blocked, id=9360, stack(0x000000d9afa00000,0x000000d9afb00000) (1024K)]
  0x000001dc587b76b0 JavaThread "Worker-4"                          [_thread_blocked, id=10204, stack(0x000000d9afb00000,0x000000d9afc00000) (1024K)]
  0x000001dc587b8a60 JavaThread "Thread-2"                   daemon [_thread_in_native, id=26348, stack(0x000000d9afc00000,0x000000d9afd00000) (1024K)]
  0x000001dc587b7d40 JavaThread "Thread-3"                   daemon [_thread_in_native, id=24780, stack(0x000000d9afd00000,0x000000d9afe00000) (1024K)]
  0x000001dc587b83d0 JavaThread "Thread-4"                   daemon [_thread_in_native, id=22232, stack(0x000000d9afe00000,0x000000d9aff00000) (1024K)]
  0x000001dc5a211570 JavaThread "Thread-5"                   daemon [_thread_in_native, id=5264, stack(0x000000d9aff00000,0x000000d9b0000000) (1024K)]
  0x000001dc5a211c00 JavaThread "Thread-6"                   daemon [_thread_in_native, id=5872, stack(0x000000d9b0000000,0x000000d9b0100000) (1024K)]
  0x000001dc5a212290 JavaThread "Thread-7"                   daemon [_thread_in_native, id=26872, stack(0x000000d9b0100000,0x000000d9b0200000) (1024K)]
  0x000001dc5a212920 JavaThread "Thread-8"                   daemon [_thread_in_native, id=3080, stack(0x000000d9b0200000,0x000000d9b0300000) (1024K)]
  0x000001dc5a212fb0 JavaThread "Thread-9"                   daemon [_thread_in_native, id=18060, stack(0x000000d9b0300000,0x000000d9b0400000) (1024K)]
  0x000001dc5a213640 JavaThread "Thread-10"                  daemon [_thread_in_native, id=27464, stack(0x000000d9b0400000,0x000000d9b0500000) (1024K)]
  0x000001dc5a2101c0 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=10052, stack(0x000000d9b0500000,0x000000d9b0600000) (1024K)]
  0x000001dc5a213cd0 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=25236, stack(0x000000d9b0600000,0x000000d9b0700000) (1024K)]
  0x000001dc5a214360 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=18120, stack(0x000000d9b0700000,0x000000d9b0800000) (1024K)]
  0x000001dc5a210850 JavaThread "Worker-5"                          [_thread_blocked, id=8488, stack(0x000000d9b0800000,0x000000d9b0900000) (1024K)]
  0x000001dc5a216ac0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=24380, stack(0x000000d9ae300000,0x000000d9ae400000) (1024K)]
  0x000001dc5a216430 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=12672, stack(0x000000d9ae400000,0x000000d9ae500000) (1024K)]
  0x000001dc5a2177e0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=20292, stack(0x000000d9ae600000,0x000000d9ae700000) (1024K)]
  0x000001dc5a215710 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=20468, stack(0x000000d9ae700000,0x000000d9ae800000) (1024K)]
  0x000001dc5a215da0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=18932, stack(0x000000d9aec00000,0x000000d9aed00000) (1024K)]
  0x000001dc7f82eab0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=20676, stack(0x000000d9aed00000,0x000000d9aee00000) (1024K)]
  0x000001dc7f8304f0 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=18964, stack(0x000000d9aee00000,0x000000d9aef00000) (1024K)]
  0x000001dc7f82d070 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=19324, stack(0x000000d9b0900000,0x000000d9b0a00000) (1024K)]
  0x000001dc7f82d700 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=20736, stack(0x000000d9b0a00000,0x000000d9b0b00000) (1024K)]
  0x000001dc7f82dd90 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=3164, stack(0x000000d9b0b00000,0x000000d9b0c00000) (1024K)]
  0x000001dc7f82e420 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=16300, stack(0x000000d9b0c00000,0x000000d9b0d00000) (1024K)]
  0x000001dc587b55e0 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=24328, stack(0x000000d9b0d00000,0x000000d9b0e00000) (1024K)]
  0x000001dc5aa53120 JavaThread "Compiler Processing Task"   daemon [_thread_blocked, id=3604, stack(0x000000d9b0e00000,0x000000d9b0f00000) (1024K)]
  0x000001dc5aa57950 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=10568, stack(0x000000d9af500000,0x000000d9af600000) (1024K)]
  0x000001dc5aa59390 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=11236, stack(0x000000d9b1700000,0x000000d9b1800000) (1024K)]
  0x000001dc5aa55f10 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=9472, stack(0x000000d9b1800000,0x000000d9b1900000) (1024K)]
  0x000001dc5aa57fe0 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=21928, stack(0x000000d9b1900000,0x000000d9b1a00000) (1024K)]
  0x000001dc5aa56c30 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=7064, stack(0x000000d9af000000,0x000000d9af100000) (1024K)]
  0x000001dc5aababc0 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=2760, stack(0x000000d9b1a00000,0x000000d9b1b00000) (1024K)]
  0x000001dc5aabcc90 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=7136, stack(0x000000d9aca00000,0x000000d9acb00000) (1024K)]
Total: 58

Other Threads:
  0x000001dc7a481be0 VMThread "VM Thread"                           [id=12144, stack(0x000000d9acf00000,0x000000d9ad000000) (1024K)]
  0x000001dc7a3bc950 WatcherThread "VM Periodic Task Thread"        [id=15324, stack(0x000000d9ace00000,0x000000d9acf00000) (1024K)]
  0x000001dc7a552b50 WorkerThread "GC Thread#0"                     [id=27296, stack(0x000000d9acd00000,0x000000d9ace00000) (1024K)]
  0x000001dc5813dc50 WorkerThread "GC Thread#1"                     [id=26668, stack(0x000000d9adc00000,0x000000d9add00000) (1024K)]
  0x000001dc5813dff0 WorkerThread "GC Thread#2"                     [id=27408, stack(0x000000d9add00000,0x000000d9ade00000) (1024K)]
  0x000001dc588002f0 WorkerThread "GC Thread#3"                     [id=19748, stack(0x000000d9ade00000,0x000000d9adf00000) (1024K)]
  0x000001dc58800690 WorkerThread "GC Thread#4"                     [id=25632, stack(0x000000d9adf00000,0x000000d9ae000000) (1024K)]
  0x000001dc5831acd0 WorkerThread "GC Thread#5"                     [id=22552, stack(0x000000d9ae000000,0x000000d9ae100000) (1024K)]
  0x000001dc5831bb50 WorkerThread "GC Thread#6"                     [id=22396, stack(0x000000d9ae100000,0x000000d9ae200000) (1024K)]
  0x000001dc5831a930 WorkerThread "GC Thread#7"                     [id=26800, stack(0x000000d9aea00000,0x000000d9aeb00000) (1024K)]
Total: 10

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000001dc13000000-0x000001dc13ba0000-0x000001dc13ba0000), size 12189696, SharedBaseAddress: 0x000001dc13000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001dc14000000-0x000001dc54000000, reserved size: 1073741824
Narrow klass base: 0x000001dc13000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 16086M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 3072K, used 2918K [0x00000000d5580000, 0x00000000d5980000, 0x0000000100000000)
  eden space 2560K, 97% used [0x00000000d5580000,0x00000000d57f1960,0x00000000d5800000)
  from space 512K, 81% used [0x00000000d5880000,0x00000000d58e8000,0x00000000d5900000)
  to   space 512K, 0% used [0x00000000d5900000,0x00000000d5900000,0x00000000d5980000)
 ParOldGen       total 533504K, used 533304K [0x0000000080000000, 0x00000000a0900000, 0x00000000d5580000)
  object space 533504K, 99% used [0x0000000080000000,0x00000000a08ce3b0,0x00000000a0900000)
 Metaspace       used 66740K, committed 68224K, reserved 1114112K
  class space    used 6733K, committed 7360K, reserved 1048576K

Card table byte_map: [0x000001dc79ed0000,0x000001dc7a2e0000] _byte_map_base: 0x000001dc79ad0000

Marking Bits: (ParMarkBitMap*) 0x00007ff9cf8f31f0
 Begin Bits: [0x000001dc0f000000, 0x000001dc11000000)
 End Bits:   [0x000001dc11000000, 0x000001dc13000000)

Polling page: 0x000001dc78450000

Metaspace:

Usage:
  Non-class:     58.60 MB used.
      Class:      6.58 MB used.
       Both:     65.18 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      59.44 MB ( 93%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       7.19 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      66.62 MB (  6%) committed. 

Chunk freelists:
   Non-Class:  3.97 MB
       Class:  8.83 MB
        Both:  12.80 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 97.31 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 1282.
num_arena_deaths: 14.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 1065.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 23.
num_chunks_taken_from_freelist: 4446.
num_chunk_merges: 12.
num_chunk_splits: 2736.
num_chunks_enlarged: 1527.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=12046Kb max_used=12046Kb free=107953Kb
 bounds [0x000001dc07ad0000, 0x000001dc086a0000, 0x000001dc0f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=32163Kb max_used=32163Kb free=87836Kb
 bounds [0x000001dc00000000, 0x000001dc01f70000, 0x000001dc07530000]
CodeHeap 'non-nmethods': size=5760Kb used=1452Kb max_used=1587Kb free=4307Kb
 bounds [0x000001dc07530000, 0x000001dc077a0000, 0x000001dc07ad0000]
 total_blobs=14048 nmethods=13298 adapters=654
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 188.155 Thread 0x000001dc7a4c0d30 nmethod 14161 0x000001dc08669c10 code [0x000001dc08669e20, 0x000001dc0866a5c8]
Event: 188.300 Thread 0x000001dc7a4c9b10 14162       3       org.eclipse.jdt.internal.compiler.classfmt.MethodInfo::createMethod (710 bytes)
Event: 188.306 Thread 0x000001dc7a4c9b10 nmethod 14162 0x000001dc01f60510 code [0x000001dc01f60a00, 0x000001dc01f638f0]
Event: 188.307 Thread 0x000001dc7a4c9b10 14163  s    3       org.eclipse.jdt.internal.compiler.classfmt.MethodInfo::readModifierRelatedAttributes (213 bytes)
Event: 188.309 Thread 0x000001dc7a4c9b10 nmethod 14163 0x000001dc01f64a10 code [0x000001dc01f64c80, 0x000001dc01f65aa0]
Event: 188.330 Thread 0x000001dc7a4c9b10 14164       3       org.eclipse.jdt.internal.compiler.classfmt.FieldInfo::createField (450 bytes)
Event: 188.333 Thread 0x000001dc7a4c9b10 nmethod 14164 0x000001dc01f65f90 code [0x000001dc01f66340, 0x000001dc01f682a8]
Event: 188.379 Thread 0x000001dc5835d610 nmethod 14147% 0x000001dc0866ac90 code [0x000001dc0866b580, 0x000001dc086761e0]
Event: 188.395 Thread 0x000001dc5835d610 14165   !   4       org.eclipse.jdt.internal.compiler.classfmt.ClassFileReader::<init> (2135 bytes)
Event: 188.580 Thread 0x000001dc7a4c0d30 14166  s    4       org.eclipse.jdt.internal.compiler.classfmt.MethodInfo::readModifierRelatedAttributes (213 bytes)
Event: 188.624 Thread 0x000001dc7a4c0d30 nmethod 14166 0x000001dc0867a010 code [0x000001dc0867a220, 0x000001dc0867b540]
Event: 188.624 Thread 0x000001dc7a4c0d30 14167       4       org.eclipse.jdt.internal.compiler.classfmt.MethodInfo::createMethod (710 bytes)
Event: 188.751 Thread 0x000001dc7a4c0d30 nmethod 14167 0x000001dc0867ba10 code [0x000001dc0867bce0, 0x000001dc0867df40]
Event: 188.751 Thread 0x000001dc7a4c0d30 14168       4       org.eclipse.jdt.internal.compiler.classfmt.FieldInfo::createField (450 bytes)
Event: 188.842 Thread 0x000001dc7a4c0d30 nmethod 14168 0x000001dc0867eb10 code [0x000001dc0867ed80, 0x000001dc08680648]
Event: 189.757 Thread 0x000001dc5835d610 nmethod 14165 0x000001dc08680f10 code [0x000001dc086817c0, 0x000001dc0868bbf8]
Event: 189.855 Thread 0x000001dc5835d610 14169       4       org.eclipse.jdt.internal.compiler.classfmt.ClassFileReader::getMemberTypes (151 bytes)
Event: 189.956 Thread 0x000001dc5835d610 nmethod 14169 0x000001dc0868f790 code [0x000001dc0868f9e0, 0x000001dc08690960]
Event: 190.178 Thread 0x000001dc7a4c0d30 14170   !   4       org.eclipse.jdt.core.Signature::getTypeParameters (313 bytes)
Event: 190.253 Thread 0x000001dc7a4c0d30 nmethod 14170 0x000001dc08691210 code [0x000001dc086914a0, 0x000001dc086929d0]

GC Heap History (20 events):
Event: 190.238 GC heap before
{Heap before GC invocations=1051 (full 3):
 PSYoungGen      total 3584K, used 2997K [0x00000000d5580000, 0x00000000d5980000, 0x0000000100000000)
  eden space 2560K, 99% used [0x00000000d5580000,0x00000000d57fd7e0,0x00000000d5800000)
  from space 1024K, 43% used [0x00000000d5800000,0x00000000d5870000,0x00000000d5900000)
  to   space 512K, 0% used [0x00000000d5900000,0x00000000d5900000,0x00000000d5980000)
 ParOldGen       total 530432K, used 529952K [0x0000000080000000, 0x00000000a0600000, 0x00000000d5580000)
  object space 530432K, 99% used [0x0000000080000000,0x00000000a05883b0,0x00000000a0600000)
 Metaspace       used 66740K, committed 68224K, reserved 1114112K
  class space    used 6733K, committed 7360K, reserved 1048576K
}
Event: 190.241 GC heap after
{Heap after GC invocations=1051 (full 3):
 PSYoungGen      total 3072K, used 416K [0x00000000d5580000, 0x00000000d5980000, 0x0000000100000000)
  eden space 2560K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5800000)
  from space 512K, 81% used [0x00000000d5900000,0x00000000d5968000,0x00000000d5980000)
  to   space 512K, 0% used [0x00000000d5880000,0x00000000d5880000,0x00000000d5900000)
 ParOldGen       total 530432K, used 530272K [0x0000000080000000, 0x00000000a0600000, 0x00000000d5580000)
  object space 530432K, 99% used [0x0000000080000000,0x00000000a05d83b0,0x00000000a0600000)
 Metaspace       used 66740K, committed 68224K, reserved 1114112K
  class space    used 6733K, committed 7360K, reserved 1048576K
}
Event: 190.265 GC heap before
{Heap before GC invocations=1052 (full 3):
 PSYoungGen      total 3072K, used 2976K [0x00000000d5580000, 0x00000000d5980000, 0x0000000100000000)
  eden space 2560K, 100% used [0x00000000d5580000,0x00000000d5800000,0x00000000d5800000)
  from space 512K, 81% used [0x00000000d5900000,0x00000000d5968000,0x00000000d5980000)
  to   space 512K, 0% used [0x00000000d5880000,0x00000000d5880000,0x00000000d5900000)
 ParOldGen       total 530432K, used 530272K [0x0000000080000000, 0x00000000a0600000, 0x00000000d5580000)
  object space 530432K, 99% used [0x0000000080000000,0x00000000a05d83b0,0x00000000a0600000)
 Metaspace       used 66740K, committed 68224K, reserved 1114112K
  class space    used 6733K, committed 7360K, reserved 1048576K
}
Event: 190.268 GC heap after
{Heap after GC invocations=1052 (full 3):
 PSYoungGen      total 3072K, used 448K [0x00000000d5580000, 0x00000000d5980000, 0x0000000100000000)
  eden space 2560K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5800000)
  from space 512K, 87% used [0x00000000d5880000,0x00000000d58f0000,0x00000000d5900000)
  to   space 512K, 0% used [0x00000000d5900000,0x00000000d5900000,0x00000000d5980000)
 ParOldGen       total 530944K, used 530568K [0x0000000080000000, 0x00000000a0680000, 0x00000000d5580000)
  object space 530944K, 99% used [0x0000000080000000,0x00000000a06223b0,0x00000000a0680000)
 Metaspace       used 66740K, committed 68224K, reserved 1114112K
  class space    used 6733K, committed 7360K, reserved 1048576K
}
Event: 190.309 GC heap before
{Heap before GC invocations=1053 (full 3):
 PSYoungGen      total 3072K, used 3007K [0x00000000d5580000, 0x00000000d5980000, 0x0000000100000000)
  eden space 2560K, 99% used [0x00000000d5580000,0x00000000d57ffff0,0x00000000d5800000)
  from space 512K, 87% used [0x00000000d5880000,0x00000000d58f0000,0x00000000d5900000)
  to   space 512K, 0% used [0x00000000d5900000,0x00000000d5900000,0x00000000d5980000)
 ParOldGen       total 530944K, used 530568K [0x0000000080000000, 0x00000000a0680000, 0x00000000d5580000)
  object space 530944K, 99% used [0x0000000080000000,0x00000000a06223b0,0x00000000a0680000)
 Metaspace       used 66740K, committed 68224K, reserved 1114112K
  class space    used 6733K, committed 7360K, reserved 1048576K
}
Event: 190.312 GC heap after
{Heap after GC invocations=1053 (full 3):
 PSYoungGen      total 3072K, used 480K [0x00000000d5580000, 0x00000000d5980000, 0x0000000100000000)
  eden space 2560K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5800000)
  from space 512K, 93% used [0x00000000d5900000,0x00000000d5978000,0x00000000d5980000)
  to   space 512K, 0% used [0x00000000d5880000,0x00000000d5880000,0x00000000d5900000)
 ParOldGen       total 530944K, used 530872K [0x0000000080000000, 0x00000000a0680000, 0x00000000d5580000)
  object space 530944K, 99% used [0x0000000080000000,0x00000000a066e3b0,0x00000000a0680000)
 Metaspace       used 66740K, committed 68224K, reserved 1114112K
  class space    used 6733K, committed 7360K, reserved 1048576K
}
Event: 190.339 GC heap before
{Heap before GC invocations=1054 (full 3):
 PSYoungGen      total 3072K, used 3040K [0x00000000d5580000, 0x00000000d5980000, 0x0000000100000000)
  eden space 2560K, 100% used [0x00000000d5580000,0x00000000d5800000,0x00000000d5800000)
  from space 512K, 93% used [0x00000000d5900000,0x00000000d5978000,0x00000000d5980000)
  to   space 512K, 0% used [0x00000000d5880000,0x00000000d5880000,0x00000000d5900000)
 ParOldGen       total 530944K, used 530872K [0x0000000080000000, 0x00000000a0680000, 0x00000000d5580000)
  object space 530944K, 99% used [0x0000000080000000,0x00000000a066e3b0,0x00000000a0680000)
 Metaspace       used 66740K, committed 68224K, reserved 1114112K
  class space    used 6733K, committed 7360K, reserved 1048576K
}
Event: 190.342 GC heap after
{Heap after GC invocations=1054 (full 3):
 PSYoungGen      total 3072K, used 416K [0x00000000d5580000, 0x00000000d5980000, 0x0000000100000000)
  eden space 2560K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5800000)
  from space 512K, 81% used [0x00000000d5880000,0x00000000d58e8000,0x00000000d5900000)
  to   space 512K, 0% used [0x00000000d5900000,0x00000000d5900000,0x00000000d5980000)
 ParOldGen       total 531456K, used 531200K [0x0000000080000000, 0x00000000a0700000, 0x00000000d5580000)
  object space 531456K, 99% used [0x0000000080000000,0x00000000a06c03b0,0x00000000a0700000)
 Metaspace       used 66740K, committed 68224K, reserved 1114112K
  class space    used 6733K, committed 7360K, reserved 1048576K
}
Event: 190.376 GC heap before
{Heap before GC invocations=1055 (full 3):
 PSYoungGen      total 3072K, used 2976K [0x00000000d5580000, 0x00000000d5980000, 0x0000000100000000)
  eden space 2560K, 100% used [0x00000000d5580000,0x00000000d5800000,0x00000000d5800000)
  from space 512K, 81% used [0x00000000d5880000,0x00000000d58e8000,0x00000000d5900000)
  to   space 512K, 0% used [0x00000000d5900000,0x00000000d5900000,0x00000000d5980000)
 ParOldGen       total 531456K, used 531200K [0x0000000080000000, 0x00000000a0700000, 0x00000000d5580000)
  object space 531456K, 99% used [0x0000000080000000,0x00000000a06c03b0,0x00000000a0700000)
 Metaspace       used 66740K, committed 68224K, reserved 1114112K
  class space    used 6733K, committed 7360K, reserved 1048576K
}
Event: 190.380 GC heap after
{Heap after GC invocations=1055 (full 3):
 PSYoungGen      total 3072K, used 384K [0x00000000d5580000, 0x00000000d5980000, 0x0000000100000000)
  eden space 2560K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5800000)
  from space 512K, 75% used [0x00000000d5900000,0x00000000d5960000,0x00000000d5980000)
  to   space 512K, 0% used [0x00000000d5880000,0x00000000d5880000,0x00000000d5900000)
 ParOldGen       total 531968K, used 531592K [0x0000000080000000, 0x00000000a0780000, 0x00000000d5580000)
  object space 531968K, 99% used [0x0000000080000000,0x00000000a07223b0,0x00000000a0780000)
 Metaspace       used 66740K, committed 68224K, reserved 1114112K
  class space    used 6733K, committed 7360K, reserved 1048576K
}
Event: 190.408 GC heap before
{Heap before GC invocations=1056 (full 3):
 PSYoungGen      total 3072K, used 2944K [0x00000000d5580000, 0x00000000d5980000, 0x0000000100000000)
  eden space 2560K, 100% used [0x00000000d5580000,0x00000000d5800000,0x00000000d5800000)
  from space 512K, 75% used [0x00000000d5900000,0x00000000d5960000,0x00000000d5980000)
  to   space 512K, 0% used [0x00000000d5880000,0x00000000d5880000,0x00000000d5900000)
 ParOldGen       total 531968K, used 531592K [0x0000000080000000, 0x00000000a0780000, 0x00000000d5580000)
  object space 531968K, 99% used [0x0000000080000000,0x00000000a07223b0,0x00000000a0780000)
 Metaspace       used 66740K, committed 68224K, reserved 1114112K
  class space    used 6733K, committed 7360K, reserved 1048576K
}
Event: 190.411 GC heap after
{Heap after GC invocations=1056 (full 3):
 PSYoungGen      total 3072K, used 480K [0x00000000d5580000, 0x00000000d5980000, 0x0000000100000000)
  eden space 2560K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5800000)
  from space 512K, 93% used [0x00000000d5880000,0x00000000d58f8000,0x00000000d5900000)
  to   space 512K, 0% used [0x00000000d5900000,0x00000000d5900000,0x00000000d5980000)
 ParOldGen       total 531968K, used 531920K [0x0000000080000000, 0x00000000a0780000, 0x00000000d5580000)
  object space 531968K, 99% used [0x0000000080000000,0x00000000a07743b0,0x00000000a0780000)
 Metaspace       used 66740K, committed 68224K, reserved 1114112K
  class space    used 6733K, committed 7360K, reserved 1048576K
}
Event: 190.443 GC heap before
{Heap before GC invocations=1057 (full 3):
 PSYoungGen      total 3072K, used 3040K [0x00000000d5580000, 0x00000000d5980000, 0x0000000100000000)
  eden space 2560K, 100% used [0x00000000d5580000,0x00000000d5800000,0x00000000d5800000)
  from space 512K, 93% used [0x00000000d5880000,0x00000000d58f8000,0x00000000d5900000)
  to   space 512K, 0% used [0x00000000d5900000,0x00000000d5900000,0x00000000d5980000)
 ParOldGen       total 531968K, used 531920K [0x0000000080000000, 0x00000000a0780000, 0x00000000d5580000)
  object space 531968K, 99% used [0x0000000080000000,0x00000000a07743b0,0x00000000a0780000)
 Metaspace       used 66740K, committed 68224K, reserved 1114112K
  class space    used 6733K, committed 7360K, reserved 1048576K
}
Event: 190.447 GC heap after
{Heap after GC invocations=1057 (full 3):
 PSYoungGen      total 3072K, used 416K [0x00000000d5580000, 0x00000000d5980000, 0x0000000100000000)
  eden space 2560K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5800000)
  from space 512K, 81% used [0x00000000d5900000,0x00000000d5968000,0x00000000d5980000)
  to   space 512K, 0% used [0x00000000d5880000,0x00000000d5880000,0x00000000d5900000)
 ParOldGen       total 532480K, used 532280K [0x0000000080000000, 0x00000000a0800000, 0x00000000d5580000)
  object space 532480K, 99% used [0x0000000080000000,0x00000000a07ce3b0,0x00000000a0800000)
 Metaspace       used 66740K, committed 68224K, reserved 1114112K
  class space    used 6733K, committed 7360K, reserved 1048576K
}
Event: 190.483 GC heap before
{Heap before GC invocations=1058 (full 3):
 PSYoungGen      total 3072K, used 2976K [0x00000000d5580000, 0x00000000d5980000, 0x0000000100000000)
  eden space 2560K, 100% used [0x00000000d5580000,0x00000000d5800000,0x00000000d5800000)
  from space 512K, 81% used [0x00000000d5900000,0x00000000d5968000,0x00000000d5980000)
  to   space 512K, 0% used [0x00000000d5880000,0x00000000d5880000,0x00000000d5900000)
 ParOldGen       total 532480K, used 532280K [0x0000000080000000, 0x00000000a0800000, 0x00000000d5580000)
  object space 532480K, 99% used [0x0000000080000000,0x00000000a07ce3b0,0x00000000a0800000)
 Metaspace       used 66740K, committed 68224K, reserved 1114112K
  class space    used 6733K, committed 7360K, reserved 1048576K
}
Event: 190.488 GC heap after
{Heap after GC invocations=1058 (full 3):
 PSYoungGen      total 3072K, used 416K [0x00000000d5580000, 0x00000000d5980000, 0x0000000100000000)
  eden space 2560K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5800000)
  from space 512K, 81% used [0x00000000d5880000,0x00000000d58e8000,0x00000000d5900000)
  to   space 512K, 0% used [0x00000000d5900000,0x00000000d5900000,0x00000000d5980000)
 ParOldGen       total 532992K, used 532640K [0x0000000080000000, 0x00000000a0880000, 0x00000000d5580000)
  object space 532992K, 99% used [0x0000000080000000,0x00000000a08283b0,0x00000000a0880000)
 Metaspace       used 66740K, committed 68224K, reserved 1114112K
  class space    used 6733K, committed 7360K, reserved 1048576K
}
Event: 190.526 GC heap before
{Heap before GC invocations=1059 (full 3):
 PSYoungGen      total 3072K, used 2976K [0x00000000d5580000, 0x00000000d5980000, 0x0000000100000000)
  eden space 2560K, 100% used [0x00000000d5580000,0x00000000d5800000,0x00000000d5800000)
  from space 512K, 81% used [0x00000000d5880000,0x00000000d58e8000,0x00000000d5900000)
  to   space 512K, 0% used [0x00000000d5900000,0x00000000d5900000,0x00000000d5980000)
 ParOldGen       total 532992K, used 532640K [0x0000000080000000, 0x00000000a0880000, 0x00000000d5580000)
  object space 532992K, 99% used [0x0000000080000000,0x00000000a08283b0,0x00000000a0880000)
 Metaspace       used 66740K, committed 68224K, reserved 1114112K
  class space    used 6733K, committed 7360K, reserved 1048576K
}
Event: 190.528 GC heap after
{Heap after GC invocations=1059 (full 3):
 PSYoungGen      total 3072K, used 416K [0x00000000d5580000, 0x00000000d5980000, 0x0000000100000000)
  eden space 2560K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5800000)
  from space 512K, 81% used [0x00000000d5900000,0x00000000d5968000,0x00000000d5980000)
  to   space 512K, 0% used [0x00000000d5880000,0x00000000d5880000,0x00000000d5900000)
 ParOldGen       total 532992K, used 532968K [0x0000000080000000, 0x00000000a0880000, 0x00000000d5580000)
  object space 532992K, 99% used [0x0000000080000000,0x00000000a087a3b0,0x00000000a0880000)
 Metaspace       used 66740K, committed 68224K, reserved 1114112K
  class space    used 6733K, committed 7360K, reserved 1048576K
}
Event: 190.549 GC heap before
{Heap before GC invocations=1060 (full 3):
 PSYoungGen      total 3072K, used 2976K [0x00000000d5580000, 0x00000000d5980000, 0x0000000100000000)
  eden space 2560K, 100% used [0x00000000d5580000,0x00000000d5800000,0x00000000d5800000)
  from space 512K, 81% used [0x00000000d5900000,0x00000000d5968000,0x00000000d5980000)
  to   space 512K, 0% used [0x00000000d5880000,0x00000000d5880000,0x00000000d5900000)
 ParOldGen       total 532992K, used 532968K [0x0000000080000000, 0x00000000a0880000, 0x00000000d5580000)
  object space 532992K, 99% used [0x0000000080000000,0x00000000a087a3b0,0x00000000a0880000)
 Metaspace       used 66740K, committed 68224K, reserved 1114112K
  class space    used 6733K, committed 7360K, reserved 1048576K
}
Event: 190.552 GC heap after
{Heap after GC invocations=1060 (full 3):
 PSYoungGen      total 3072K, used 416K [0x00000000d5580000, 0x00000000d5980000, 0x0000000100000000)
  eden space 2560K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5800000)
  from space 512K, 81% used [0x00000000d5880000,0x00000000d58e8000,0x00000000d5900000)
  to   space 512K, 0% used [0x00000000d5900000,0x00000000d5900000,0x00000000d5980000)
 ParOldGen       total 533504K, used 533304K [0x0000000080000000, 0x00000000a0900000, 0x00000000d5580000)
  object space 533504K, 99% used [0x0000000080000000,0x00000000a08ce3b0,0x00000000a0900000)
 Metaspace       used 66740K, committed 68224K, reserved 1114112K
  class space    used 6733K, committed 7360K, reserved 1048576K
}

Dll operation events (10 events):
Event: 0.016 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.045 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.116 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.124 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.127 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.133 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.155 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.264 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
Event: 2.472 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 4.911 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-3599307\jna6448034883491008667.dll

Deoptimization events (20 events):
Event: 189.211 Thread 0x000001dc587b9e10 DEOPT PACKING pc=0x000001dc01f3ceaa sp=0x000000d9af6fea20
Event: 189.211 Thread 0x000001dc587b9e10 DEOPT UNPACKING pc=0x000001dc075878e2 sp=0x000000d9af6fdfa8 mode 0
Event: 189.248 Thread 0x000001dc587b9e10 DEOPT PACKING pc=0x000001dc01f3ceaa sp=0x000000d9af6fea20
Event: 189.248 Thread 0x000001dc587b9e10 DEOPT UNPACKING pc=0x000001dc075878e2 sp=0x000000d9af6fdfa8 mode 0
Event: 189.276 Thread 0x000001dc587b9e10 DEOPT PACKING pc=0x000001dc01f3ceaa sp=0x000000d9af6fea20
Event: 189.276 Thread 0x000001dc587b9e10 DEOPT UNPACKING pc=0x000001dc075878e2 sp=0x000000d9af6fdfa8 mode 0
Event: 189.323 Thread 0x000001dc587b9e10 DEOPT PACKING pc=0x000001dc01f3ceaa sp=0x000000d9af6fea20
Event: 189.323 Thread 0x000001dc587b9e10 DEOPT UNPACKING pc=0x000001dc075878e2 sp=0x000000d9af6fdfa8 mode 0
Event: 189.362 Thread 0x000001dc587b9e10 DEOPT PACKING pc=0x000001dc01f3ceaa sp=0x000000d9af6fea20
Event: 189.362 Thread 0x000001dc587b9e10 DEOPT UNPACKING pc=0x000001dc075878e2 sp=0x000000d9af6fdfa8 mode 0
Event: 189.390 Thread 0x000001dc587b9e10 DEOPT PACKING pc=0x000001dc01f3ceaa sp=0x000000d9af6fea20
Event: 189.390 Thread 0x000001dc587b9e10 DEOPT UNPACKING pc=0x000001dc075878e2 sp=0x000000d9af6fdfa8 mode 0
Event: 189.467 Thread 0x000001dc587b9e10 DEOPT PACKING pc=0x000001dc01f3ceaa sp=0x000000d9af6fea20
Event: 189.467 Thread 0x000001dc587b9e10 DEOPT UNPACKING pc=0x000001dc075878e2 sp=0x000000d9af6fdfa8 mode 0
Event: 189.568 Thread 0x000001dc587b9e10 DEOPT PACKING pc=0x000001dc01f3ceaa sp=0x000000d9af6fea20
Event: 189.568 Thread 0x000001dc587b9e10 DEOPT UNPACKING pc=0x000001dc075878e2 sp=0x000000d9af6fdfa8 mode 0
Event: 189.663 Thread 0x000001dc587b9e10 DEOPT PACKING pc=0x000001dc01f3ceaa sp=0x000000d9af6fea20
Event: 189.663 Thread 0x000001dc587b9e10 DEOPT UNPACKING pc=0x000001dc075878e2 sp=0x000000d9af6fdfa8 mode 0
Event: 189.729 Thread 0x000001dc587b9e10 DEOPT PACKING pc=0x000001dc01f3ceaa sp=0x000000d9af6fea20
Event: 189.729 Thread 0x000001dc587b9e10 DEOPT UNPACKING pc=0x000001dc075878e2 sp=0x000000d9af6fdfa8 mode 0

Classes loaded (20 events):
Event: 164.445 Loading class java/util/TreeMap$TreeMapSpliterator done
Event: 164.445 Loading class java/util/TreeMap$ValueSpliterator done
Event: 164.582 Loading class java/util/concurrent/CompletionException
Event: 164.582 Loading class java/util/concurrent/CompletionException done
Event: 164.621 Loading class java/nio/file/FileTreeWalker$1
Event: 164.622 Loading class java/nio/file/FileTreeWalker$1 done
Event: 164.624 Loading class sun/nio/fs/Globs
Event: 164.627 Loading class sun/nio/fs/Globs done
Event: 164.627 Loading class java/util/regex/Pattern$SliceU
Event: 164.628 Loading class java/util/regex/Pattern$SliceU done
Event: 164.628 Loading class sun/nio/fs/WindowsFileSystem$2
Event: 164.628 Loading class java/nio/file/PathMatcher
Event: 164.629 Loading class java/nio/file/PathMatcher done
Event: 164.629 Loading class sun/nio/fs/WindowsFileSystem$2 done
Event: 167.050 Loading class java/util/Collections$SynchronizedRandomAccessList
Event: 167.051 Loading class java/util/Collections$SynchronizedList
Event: 167.051 Loading class java/util/Collections$SynchronizedList done
Event: 167.051 Loading class java/util/Collections$SynchronizedRandomAccessList done
Event: 167.469 Loading class java/util/concurrent/CompletableFuture$Signaller
Event: 167.469 Loading class java/util/concurrent/CompletableFuture$Signaller done

Classes unloaded (7 events):
Event: 5.809 Thread 0x000001dc7a481be0 Unloading class 0x000001dc141a8400 'java/lang/invoke/LambdaForm$MH+0x000001dc141a8400'
Event: 5.809 Thread 0x000001dc7a481be0 Unloading class 0x000001dc141a8000 'java/lang/invoke/LambdaForm$MH+0x000001dc141a8000'
Event: 5.809 Thread 0x000001dc7a481be0 Unloading class 0x000001dc141a7c00 'java/lang/invoke/LambdaForm$MH+0x000001dc141a7c00'
Event: 5.809 Thread 0x000001dc7a481be0 Unloading class 0x000001dc141a7800 'java/lang/invoke/LambdaForm$MH+0x000001dc141a7800'
Event: 5.809 Thread 0x000001dc7a481be0 Unloading class 0x000001dc141a7400 'java/lang/invoke/LambdaForm$BMH+0x000001dc141a7400'
Event: 5.809 Thread 0x000001dc7a481be0 Unloading class 0x000001dc141a7000 'java/lang/invoke/LambdaForm$DMH+0x000001dc141a7000'
Event: 5.809 Thread 0x000001dc7a481be0 Unloading class 0x000001dc141a6000 'java/lang/invoke/LambdaForm$DMH+0x000001dc141a6000'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 184.531 Thread 0x000001dc587b9e10 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d56be0a8}> (0x00000000d56be0a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 184.657 Thread 0x000001dc587b9e10 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d56fcb40}> (0x00000000d56fcb40) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 185.044 Thread 0x000001dc587b9e10 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5739280}> (0x00000000d5739280) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 185.091 Thread 0x000001dc587b9e10 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d55f5018}> (0x00000000d55f5018) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 185.148 Thread 0x000001dc587b9e10 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5687dd8}> (0x00000000d5687dd8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 185.607 Thread 0x000001dc587b9e10 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d561c558}> (0x00000000d561c558) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 185.648 Thread 0x000001dc587b9e10 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5652688}> (0x00000000d5652688) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 185.899 Thread 0x000001dc587b9e10 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5642bc0}> (0x00000000d5642bc0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 185.932 Thread 0x000001dc587b9e10 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d55c3870}> (0x00000000d55c3870) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 186.044 Thread 0x000001dc587b9e10 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d55c1b98}> (0x00000000d55c1b98) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 186.075 Thread 0x000001dc587b9e10 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d55dc440}> (0x00000000d55dc440) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 186.211 Thread 0x000001dc587b9e10 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5590350}> (0x00000000d5590350) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 186.241 Thread 0x000001dc587b9e10 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d55eaeb8}> (0x00000000d55eaeb8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 186.311 Thread 0x000001dc587b9e10 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5629378}> (0x00000000d5629378) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 186.335 Thread 0x000001dc587b9e10 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5657370}> (0x00000000d5657370) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 186.598 Thread 0x000001dc587b9e10 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5652fc0}> (0x00000000d5652fc0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 186.725 Thread 0x000001dc587b9e10 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5645838}> (0x00000000d5645838) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 186.865 Thread 0x000001dc587b9e10 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d55bfaa0}> (0x00000000d55bfaa0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 186.920 Thread 0x000001dc587b9e10 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d560e040}> (0x00000000d560e040) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 190.580 Thread 0x000001dc587b9e10 Implicit null exception at 0x000001dc07dd3c23 to 0x0000000000000000

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 190.238 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 190.241 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 190.265 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 190.268 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 190.309 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 190.312 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 190.339 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 190.342 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 190.376 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 190.380 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 190.408 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 190.411 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 190.443 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 190.447 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 190.483 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 190.488 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 190.526 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 190.528 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 190.549 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 190.552 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 26.820 Thread 0x000001dc7a481be0 flushing osr nmethod 0x000001dc009aef10
Event: 26.820 Thread 0x000001dc7a481be0 flushing osr nmethod 0x000001dc009b3e10
Event: 26.820 Thread 0x000001dc7a481be0 flushing osr nmethod 0x000001dc009eea10
Event: 26.820 Thread 0x000001dc7a481be0 flushing  nmethod 0x000001dc00a04610
Event: 26.820 Thread 0x000001dc7a481be0 flushing  nmethod 0x000001dc00a06f90
Event: 26.820 Thread 0x000001dc7a481be0 flushing  nmethod 0x000001dc00a08b90
Event: 26.820 Thread 0x000001dc7a481be0 flushing  nmethod 0x000001dc00a09410
Event: 26.820 Thread 0x000001dc7a481be0 flushing osr nmethod 0x000001dc00a0b810
Event: 26.820 Thread 0x000001dc7a481be0 flushing  nmethod 0x000001dc00a10110
Event: 26.820 Thread 0x000001dc7a481be0 flushing  nmethod 0x000001dc00a19a90
Event: 26.820 Thread 0x000001dc7a481be0 flushing  nmethod 0x000001dc00a21a10
Event: 26.820 Thread 0x000001dc7a481be0 flushing  nmethod 0x000001dc00a2d210
Event: 26.820 Thread 0x000001dc7a481be0 flushing  nmethod 0x000001dc00a2eb10
Event: 26.820 Thread 0x000001dc7a481be0 flushing  nmethod 0x000001dc00a2f110
Event: 26.820 Thread 0x000001dc7a481be0 flushing  nmethod 0x000001dc00a2fb90
Event: 26.820 Thread 0x000001dc7a481be0 flushing  nmethod 0x000001dc00a33910
Event: 26.820 Thread 0x000001dc7a481be0 flushing  nmethod 0x000001dc00a37c10
Event: 26.820 Thread 0x000001dc7a481be0 flushing  nmethod 0x000001dc00a4bd10
Event: 26.820 Thread 0x000001dc7a481be0 flushing  nmethod 0x000001dc00a57c90
Event: 26.820 Thread 0x000001dc7a481be0 flushing  nmethod 0x000001dc00a84110

Events (20 events):
Event: 127.200 Thread 0x000001dc5aabc600 Thread exited: 0x000001dc5aabc600
Event: 127.201 Thread 0x000001dc5aabbf70 Thread exited: 0x000001dc5aabbf70
Event: 127.201 Thread 0x000001dc5aabcc90 Thread exited: 0x000001dc5aabcc90
Event: 127.715 Thread 0x000001dc5835c1a0 Thread exited: 0x000001dc5835c1a0
Event: 128.650 Thread 0x000001dc5a216430 Thread added: 0x000001dc5aabcc90
Event: 155.587 Thread 0x000001dc7a4c9b10 Thread added: 0x000001dc5835c1a0
Event: 156.174 Thread 0x000001dc7a4c9b10 Thread added: 0x000001dc5835a660
Event: 160.033 Thread 0x000001dc5835a660 Thread exited: 0x000001dc5835a660
Event: 161.526 Thread 0x000001dc7a4c9b10 Thread added: 0x000001dc5835dce0
Event: 162.243 Thread 0x000001dc5835dce0 Thread exited: 0x000001dc5835dce0
Event: 162.474 Thread 0x000001dc5835c1a0 Thread exited: 0x000001dc5835c1a0
Event: 162.981 Thread 0x000001dc7a4c9b10 Thread added: 0x000001dc5835bad0
Event: 163.958 Thread 0x000001dc5835bad0 Thread exited: 0x000001dc5835bad0
Event: 167.101 Thread 0x000001dc7a4c9b10 Thread added: 0x000001dc5835bad0
Event: 167.350 Thread 0x000001dc5835bad0 Thread exited: 0x000001dc5835bad0
Event: 179.651 Thread 0x000001dc7a4c9b10 Thread added: 0x000001dc5835c1a0
Event: 182.118 Thread 0x000001dc5835c1a0 Thread exited: 0x000001dc5835c1a0
Event: 186.832 Thread 0x000001dc7a4c9b10 Thread added: 0x000001dc5835d610
Event: 188.677 Thread 0x000001dc5a217150 Thread exited: 0x000001dc5a217150
Event: 190.178 Thread 0x000001dc5835d610 Thread exited: 0x000001dc5835d610


Dynamic libraries:
0x00007ff665210000 - 0x00007ff66521e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ffa72040000 - 0x00007ffa722a5000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffa70ec0000 - 0x00007ffa70f89000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffa6f940000 - 0x00007ffa6fd28000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffa6f310000 - 0x00007ffa6f45b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffa5c110000 - 0x00007ffa5c12e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffa5c1b0000 - 0x00007ffa5c1c8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ffa71a20000 - 0x00007ffa71bea000 	C:\WINDOWS\System32\USER32.dll
0x00007ffa6f460000 - 0x00007ffa6f487000 	C:\WINDOWS\System32\win32u.dll
0x00007ffa6fee0000 - 0x00007ffa6ff0b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffa6f5f0000 - 0x00007ffa6f727000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffa59940000 - 0x00007ffa59bda000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007ffa6fd30000 - 0x00007ffa6fdd3000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffa70c60000 - 0x00007ffa70d09000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffa71010000 - 0x00007ffa71040000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffa5c2a0000 - 0x00007ffa5c2ac000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffa0b690000 - 0x00007ffa0b71d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007ff9cec40000 - 0x00007ff9cf9d0000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ffa71d60000 - 0x00007ffa71e13000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffa70a20000 - 0x00007ffa70ac6000 	C:\WINDOWS\System32\sechost.dll
0x00007ffa71050000 - 0x00007ffa71165000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffa719a0000 - 0x00007ffa71a14000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffa6e820000 - 0x00007ffa6e87e000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffa56e40000 - 0x00007ffa56e75000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffa5e810000 - 0x00007ffa5e81b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffa6e660000 - 0x00007ffa6e674000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffa6df50000 - 0x00007ffa6df6b000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffa5c170000 - 0x00007ffa5c17a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ffa6c960000 - 0x00007ffa6cba1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffa70100000 - 0x00007ffa70485000 	C:\WINDOWS\System32\combase.dll
0x00007ffa71c70000 - 0x00007ffa71d51000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffa44710000 - 0x00007ffa44749000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffa6f550000 - 0x00007ffa6f5e9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffa5bdb0000 - 0x00007ffa5bdbf000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ffa5b4d0000 - 0x00007ffa5b4ef000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ffa71250000 - 0x00007ffa71992000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffa6f7c0000 - 0x00007ffa6f934000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffa6ce30000 - 0x00007ffa6d688000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffa6fde0000 - 0x00007ffa6fed1000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffa71170000 - 0x00007ffa711da000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffa6f0a0000 - 0x00007ffa6f0cf000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffa55820000 - 0x00007ffa55838000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ffa5a160000 - 0x00007ffa5a170000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ffa61fd0000 - 0x00007ffa620ee000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffa6e530000 - 0x00007ffa6e59a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffa53a20000 - 0x00007ffa53a36000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ffa59530000 - 0x00007ffa59540000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
0x00007ffa088e0000 - 0x00007ffa08925000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ffa70d10000 - 0x00007ffa70eae000 	C:\WINDOWS\System32\ole32.dll
0x00007ffa6e890000 - 0x00007ffa6e8ab000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffa6deb0000 - 0x00007ffa6deea000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffa6e5d0000 - 0x00007ffa6e5fb000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffa6f070000 - 0x00007ffa6f096000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffa6e8b0000 - 0x00007ffa6e8bc000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffa6d9d0000 - 0x00007ffa6da03000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffa70ad0000 - 0x00007ffa70ada000 	C:\WINDOWS\System32\NSI.dll
0x00007ffa08840000 - 0x00007ffa08889000 	C:\Users\<USER>\AppData\Local\Temp\jna-3599307\jna6448034883491008667.dll
0x00007ffa71240000 - 0x00007ffa71248000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffa61fa0000 - 0x00007ffa61fbf000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffa61f30000 - 0x00007ffa61f55000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffa6e070000 - 0x00007ffa6e0a6000 	C:\WINDOWS\SYSTEM32\ntmarta.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702;C:\Users\<USER>\AppData\Local\Temp\jna-3599307

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8549aeba6249e2d1a9ef117416a9d08e\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8549aeba6249e2d1a9ef117416a9d08e\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-43fa0094dfa2d4a981dce5d72ba972d4-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8549aeba6249e2d1a9ef117416a9d08e\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\apache-maven-3.9.9\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;D:\Program Files\IntelliJ IDEA Community Edition 2023.3.1\bin;
USERNAME=user
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 126 Stepping 5, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
OS uptime: 1 days 16:52 hours

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 126 stepping 5 microcode 0xa0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, avx512_vbmi2, avx512_vbmi, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, pku, avx512_ifma
Processor Information for the first 8 processors :
  Max Mhz: 1190, Current Mhz: 991, Mhz Limit: 987

Memory: 4k page, system-wide physical 16086M (836M free)
TotalPageFile size 22230M (AvailPageFile size 821M)
current process WorkingSet (physical memory assigned to process): 902M, peak: 902M
current process commit charge ("private bytes"): 854M, peak: 854M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
