# MessageTools 死亡消息全服通报功能

## 🎯 功能概述

MessageTools现在支持死亡消息全服通报功能，通过Velocity代理实现跨服务器的死亡消息广播。

### 架构设计
```
Paper服务器 → 死亡事件监听 → 插件消息通道 → Velocity代理 → 全服广播
```

## 🔧 技术实现

### Velocity端 (代理端)
- **DeathMessageService** - 处理来自Paper端的死亡消息
- **PluginMessageListener** - 监听插件消息通道
- **死亡消息格式化** - 支持Unicode字符和变量替换
- **全服广播** - 发送到所有在线玩家

### Paper端 (服务器端)
- **DeathEventListener** - 监听玩家死亡事件
- **DeathCauseAnalyzer** - 分析死亡原因和武器
- **DeathMessageService** - 处理死亡消息并发送到代理
- **插件消息通道** - 与Velocity通信

## 📋 支持的死亡原因

### 自然死亡
- **fall** - 摔死
- **drown** - 溺水身亡
- **lava** - 被岩浆烧死
- **fire** - 被火烧死
- **suffocation** - 窒息而死
- **void** - 掉入虚空
- **starve** - 饿死
- **poison** - 中毒身亡

### 生物攻击
- **mob** - 被怪物杀死
- **zombie** - 被僵尸杀死
- **skeleton** - 被骷髅射死
- **spider** - 被蜘蛛杀死
- **creeper** - 被苦力怕炸死
- **enderman** - 被末影人杀死

### 玩家PvP
- **player** - 被玩家近战攻击杀死
- **player_bow** - 被玩家弓箭射死

### 爆炸
- **explosion** - 被炸死
- **tnt** - 被TNT炸死

### 其他
- **magic** - 被魔法杀死
- **wither** - 被凋零杀死
- **projectile** - 被投射物杀死

## 🎨 消息格式配置

### Velocity端配置 (config.yml)
```yaml
death_messages:
  enabled: true
  broadcast_to_all_servers: true
  include_source_server: true
  console_output: true
  default_format: "&c{unicode:skull} &f%player% &7死亡了"
  
  formats:
    fall: "&c{unicode:skull} &f%player% &7摔死了"
    drown: "&c{unicode:skull} &f%player% &7溺水身亡"
    player: "&c{unicode:sword} &f%player% &7被 &c%weapon% &7杀死了"
    creeper: "&c{unicode:skull} &f%player% &7被苦力怕炸死了"
    # ... 更多死亡原因
```

### Paper端配置 (config.yml)
```yaml
death_messages:
  enabled: true
  cancel_original: true  # 取消原始死亡消息
  
weapon_translations:
  diamond_sword: "钻石剑"
  bow: "弓"
  # ... 更多武器翻译
```

## 🔄 消息流程

### 1. 玩家死亡
```
玩家在Paper服务器死亡 → DeathEventListener监听到事件
```

### 2. 死亡分析
```
DeathCauseAnalyzer分析:
- 死亡原因 (fall, player, creeper等)
- 武器名称 (如果是PvP)
- 杀手信息 (如果适用)
```

### 3. 消息发送
```
Paper端 → 插件消息通道 → Velocity代理
数据格式: "playerName|deathCause|weapon|sourceServer"
```

### 4. 全服广播
```
Velocity代理 → 格式化消息 → 发送到所有在线玩家
```

## 📊 配置示例

### 基础配置
```yaml
# Velocity端
death_messages:
  enabled: true
  formats:
    player: "&c⚔ &f%player% &7被 &c%weapon% &7杀死了"
    fall: "&c💀 &f%player% &7摔死了"
    creeper: "&c💥 &f%player% &7被苦力怕炸死了"

# Paper端  
death_messages:
  enabled: true
  cancel_original: true
```

### 高级配置
```yaml
# Velocity端 - 使用Unicode字符
death_messages:
  formats:
    player: "&c{unicode:sword} &f%player% &7被 &c%weapon% &7杀死了"
    player_bow: "&c{unicode:bow} &f%player% &7被 &c%weapon% &7射死了"
    fall: "&c{unicode:skull} &f%player% &7从高处摔死了"
    lava: "&c{unicode:fire} &f%player% &7被岩浆烧死了"
    creeper: "&c{unicode:explosion} &f%player% &7被苦力怕炸死了"
```

## 🎮 实际效果

### PvP死亡
```
⚔ PlayerA 被 钻石剑 杀死了
🏹 PlayerB 被 PlayerC 射死了
```

### 环境死亡
```
💀 PlayerD 摔死了
🔥 PlayerE 被岩浆烧死了
💥 PlayerF 被苦力怕炸死了
```

### 怪物攻击
```
🧟 PlayerG 被僵尸杀死了
💀 PlayerH 被骷髅射死了
🕷️ PlayerI 被蜘蛛杀死了
```

## 🔧 安装和部署

### 1. Velocity端
1. 将 `MessageTools-1.7-SNAPSHOT.jar` 放入 Velocity 的 `plugins` 目录
2. 配置 `config.yml` 中的死亡消息设置
3. 重启 Velocity 代理

### 2. Paper端
1. 将 `MessageTools-Paper-1.0-SNAPSHOT.jar` 放入每个 Paper 服务器的 `plugins` 目录
2. 配置各服务器的 `config.yml`
3. 重启所有 Paper 服务器

### 3. 验证安装
1. 在任意服务器中测试玩家死亡
2. 检查是否在所有服务器显示死亡消息
3. 查看控制台日志确认消息传输

## 🔍 故障排除

### 问题1：死亡消息不显示
**检查**：
1. Velocity端 `death_messages.enabled: true`
2. Paper端 `death_messages.enabled: true`
3. 插件消息通道是否正常注册

### 问题2：消息格式错误
**检查**：
1. 配置文件语法是否正确
2. Unicode字符是否正确配置
3. 变量名称是否匹配

### 问题3：只在部分服务器显示
**检查**：
1. 所有Paper服务器是否安装了Paper端插件
2. 网络连接是否正常
3. 插件消息通道是否畅通

## 📈 性能考虑

### 网络开销
- 每次死亡发送约50-100字节数据
- 对网络影响极小

### 服务器性能
- 死亡事件处理异步执行
- 不影响游戏性能

### 消息频率
- 支持高频死亡事件
- 自动队列管理

## 🎯 扩展功能

### 自定义死亡原因
可以通过配置添加更多死亡原因：
```yaml
formats:
  custom_trap: "&c🪤 &f%player% &7掉入了陷阱"
  dragon: "&c🐉 &f%player% &7被末影龙杀死了"
```

### 服务器特定消息
可以为不同服务器配置不同的消息格式：
```yaml
formats:
  player: "&c[%server%] &f%player% &7被 &c%weapon% &7杀死了"
```

### 统计功能
可以扩展添加死亡统计功能：
- 死亡次数统计
- 死亡原因分析
- PvP排行榜

---

通过这个死亡消息全服通报功能，你的Minecraft服务器网络现在可以实现真正的跨服务器死亡消息同步，让所有玩家都能看到网络中发生的重要事件！
