package org.plugin.messagetools.service;

import com.velocitypowered.api.proxy.ProxyServer;
import com.velocitypowered.api.proxy.server.RegisteredServer;
import net.kyori.adventure.text.Component;
import org.plugin.messagetools.config.ConfigManager;
import org.plugin.messagetools.util.ColorUtil;
import org.plugin.messagetools.util.UnicodeUtil;
import org.slf4j.Logger;

import java.util.concurrent.CompletableFuture;

/**
 * 死亡消息处理服务
 * 负责处理来自Paper端的死亡消息并转发到所有服务器
 */
public class DeathMessageService {
    
    private final ProxyServer server;
    private final ConfigManager configManager;
    private final Logger logger;
    
    public DeathMessageService(ProxyServer server, ConfigManager configManager, Logger logger) {
        this.server = server;
        this.configManager = configManager;
        this.logger = logger;
    }
    
    /**
     * 处理来自Paper端的死亡消息
     *
     * @param playerName 死亡玩家名称
     * @param deathCause 死亡原因
     * @param killerName 杀手名称（可选）
     * @param weapon 武器名称（可选）
     * @param sourceServer 来源服务器
     */
    public void handleDeathMessage(String playerName, String deathCause, String killerName, String weapon, String sourceServer) {
        boolean debugEnabled = configManager.getBoolean("debug.enabled", false);
        boolean verboseEvents = configManager.getBoolean("debug.verbose_events", false);

        if (debugEnabled) {
            logger.info("收到死亡消息: 玩家={}, 原因={}, 杀手={}, 武器={}, 来源服务器={}",
                playerName, deathCause, killerName, weapon, sourceServer);
        }
        
        // 检查死亡消息是否启用
        if (!configManager.getBoolean("death_messages.enabled", true)) {
            if (debugEnabled && verboseEvents) {
                logger.info("死亡消息功能已禁用，跳过处理");
            }
            return;
        }
        
        // 构建死亡消息
        String deathMessage = buildDeathMessage(playerName, deathCause, killerName, weapon, sourceServer);
        
        if (deathMessage == null || deathMessage.isEmpty()) {
            if (debugEnabled) {
                logger.warn("无法构建死亡消息，跳过发送");
            }
            return;
        }
        
        // 发送到所有服务器
        broadcastDeathMessage(deathMessage, sourceServer, debugEnabled, verboseEvents);
    }
    
    /**
     * 构建死亡消息
     */
    private String buildDeathMessage(String playerName, String deathCause, String killerName, String weapon, String sourceServer) {
        boolean debugEnabled = configManager.getBoolean("debug.enabled", false);

        // 获取死亡消息格式
        String format = getDeathMessageFormat(deathCause);

        if (debugEnabled) {
            logger.info("构建死亡消息 - 原因: {}, 格式: {}", deathCause, format);
            logger.info("变量 - 玩家: {}, 杀手: {}, 武器: {}", playerName, killerName, weapon);
        }

        if (format == null) {
            // 使用默认格式
            format = configManager.getString("death_messages.default_format",
                "&f%player% &7死了");

            if (debugEnabled) {
                logger.warn("未找到死亡原因 '{}' 的格式，使用默认格式: {}", deathCause, format);
            }
        }

        // 替换变量
        String message = format
            .replace("%player%", playerName)
            .replace("%killer%", killerName != null ? killerName : "")
            .replace("%weapon%", weapon != null ? weapon : "")
            .replace("%server%", sourceServer);

        // 处理Unicode字符
        message = UnicodeUtil.processUnicode(message);

        if (debugEnabled) {
            logger.info("最终死亡消息: {}", message);
        }

        return message;
    }
    
    /**
     * 获取特定死亡原因的消息格式
     */
    private String getDeathMessageFormat(String deathCause) {
        String configPath = "death_messages.formats." + deathCause;
        String format = configManager.getString(configPath, null);

        boolean debugEnabled = configManager.getBoolean("debug.enabled", false);
        if (debugEnabled) {
            logger.info("查找死亡格式 - 路径: {}, 结果: {}", configPath, format);
        }

        return format;
    }
    
    /**
     * 广播死亡消息到所有服务器
     */
    private void broadcastDeathMessage(String message, String sourceServer, boolean debugEnabled, boolean verboseEvents) {
        Component component = ColorUtil.parseColorCodes(message);
        
        // 获取广播设置
        boolean includeSource = configManager.getBoolean("death_messages.include_source_server", true);
        boolean broadcastToAll = configManager.getBoolean("death_messages.broadcast_to_all_servers", true);
        
        if (broadcastToAll) {
            // 发送到所有服务器的所有玩家
            server.getAllPlayers().forEach(player -> {
                player.sendMessage(component);
                
                if (debugEnabled && verboseEvents) {
                    logger.info("发送死亡消息给玩家: {} (服务器: {})", 
                        player.getUsername(), 
                        player.getCurrentServer().map(s -> s.getServerInfo().getName()).orElse("未知"));
                }
            });
            
            if (debugEnabled) {
                logger.info("死亡消息已广播到所有在线玩家 ({}人)", server.getPlayerCount());
            }
        } else if (!includeSource) {
            // 只发送到非来源服务器
            server.getAllPlayers().forEach(player -> {
                String playerServer = player.getCurrentServer()
                    .map(s -> s.getServerInfo().getName())
                    .orElse("");
                
                if (!playerServer.equals(sourceServer)) {
                    player.sendMessage(component);
                    
                    if (debugEnabled && verboseEvents) {
                        logger.info("发送死亡消息给玩家: {} (服务器: {})", 
                            player.getUsername(), playerServer);
                    }
                }
            });
            
            if (debugEnabled) {
                logger.info("死亡消息已发送到非来源服务器的玩家");
            }
        }
        
        // 控制台输出
        if (configManager.getBoolean("death_messages.console_output", true)) {
            logger.info("[死亡消息] {}", ColorUtil.stripColorCodes(message));
        }
    }
    
    /**
     * 处理插件消息通道接收到的死亡消息数据
     * 
     * @param data 消息数据（格式：playerName|deathCause|weapon|sourceServer）
     */
    public void processPluginMessage(String data) {
        boolean debugEnabled = configManager.getBoolean("debug.enabled", false);
        boolean verboseEvents = configManager.getBoolean("debug.verbose_events", false);
        
        if (debugEnabled && verboseEvents) {
            logger.info("收到插件消息数据: {}", data);
        }
        
        try {
            String[] parts = data.split("\\|", 5);

            if (parts.length < 4) {
                logger.warn("插件消息数据格式错误: {}", data);
                return;
            }

            String playerName = parts[0];
            String deathCause = parts[1];
            String killerName = parts.length > 2 && !parts[2].isEmpty() ? parts[2] : null;
            String weapon = parts.length > 3 && !parts[3].isEmpty() ? parts[3] : null;
            String sourceServer = parts.length > 4 ? parts[4] : "未知服务器";

            // 处理死亡消息
            handleDeathMessage(playerName, deathCause, killerName, weapon, sourceServer);
            
        } catch (Exception e) {
            logger.error("处理插件消息时发生错误: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 检查死亡消息功能是否启用
     */
    public boolean isEnabled() {
        return configManager.getBoolean("death_messages.enabled", true);
    }
    
    /**
     * 获取支持的死亡原因列表
     */
    public java.util.Set<String> getSupportedDeathCauses() {
        return configManager.getConfigurationSection("death_messages.formats")
            .map(section -> section.keySet())
            .orElse(java.util.Set.of());
    }
}
