# MessageTools 最终简化架构完成

## 🎉 返璞归真：完美的简化工作流程

经过深度重构和优化，MessageTools现在实现了你要求的完美工作流程：

**玩家死亡信息输入拦截 → MessageTools-Paper解析颜色代码 → 将解析后的死亡信息发送到代理端 → 代理端MessageTools全服通告**

## 🔄 最终工作流程

### 第一步：玩家死亡信息输入拦截 ✅
```java
@EventHandler(priority = EventPriority.HIGHEST)
public void onPlayerDeathHighest(PlayerDeathEvent event) {
    // 拦截死亡事件，获取原始死亡消息
    deathMessageService.handlePlayerDeath(event);
}

@EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
public void onPlayerDeathMonitor(PlayerDeathEvent event) {
    // 取消原始死亡消息显示，避免重复
    if (deathMessageService.shouldCancelOriginalMessage()) {
        event.deathMessage(null);
    }
}
```

### 第二步：MessageTools-Paper解析颜色代码 ✅
```java
private Component buildCompleteDeathMessage(PlayerDeathEvent event) {
    Component originalMessage = event.deathMessage();
    
    if (originalMessage != null) {
        // 直接使用原始死亡消息，应用MineDown颜色增强
        return enhanceOriginalMessageWithMineDown(originalMessage);
    }
    
    // 如果没有原始死亡消息，构建基础死亡消息
    return buildBasicDeathMessage(event);
}

private Component enhanceOriginalMessageWithMineDown(Component originalMessage) {
    // 将原始消息转换为纯文本
    String plainText = PlainTextComponentSerializer.plainText().serialize(originalMessage);
    
    // 应用基础的MineDown颜色增强
    String enhancedText = applyBasicMineDownColors(plainText);
    
    // 解析并返回增强后的消息
    return MineDownUtil.parse(enhancedText);
}
```

### 第三步：将解析后的死亡信息发送到代理端 ✅
```java
private void sendCompleteDeathMessageToProxy(String playerName, String messageJson) {
    // 构建消息格式：playerName|messageJson|serverName
    String serverName = configManager.getString("server.name", "unknown");
    String messageData = playerName + "|" + messageJson + "|" + serverName;
    
    // 序列化Component为JSON
    String messageJson = GsonComponentSerializer.gson().serialize(deathMessage);
    
    // 发送到Velocity代理
    ByteArrayDataOutput out = ByteStreams.newDataOutput();
    out.writeUTF(messageData);
    player.sendPluginMessage(plugin, DEATH_MESSAGE_CHANNEL, out.toByteArray());
}
```

### 第四步：代理端MessageTools全服通告 ✅
```java
public void handleCompleteDeathMessage(String playerName, String messageJson, String sourceServer) {
    try {
        // 反序列化死亡消息
        Component deathMessage = GsonComponentSerializer.gson().deserialize(messageJson);
        
        // 直接广播到所有服务器（避免重复发送到来源服务器）
        broadcastCompleteDeathMessage(deathMessage, sourceServer);
        
        // 控制台输出
        if (configManager.getBoolean("death_messages.console_output", true)) {
            String plainText = PlainTextComponentSerializer.plainText().serialize(deathMessage);
            logger.info("[死亡消息] {}", plainText);
        }
        
    } catch (Exception e) {
        logger.error("处理死亡消息时发生错误", e);
    }
}
```

## 🎨 MineDown颜色支持

### 完整支持的格式
1. **传统颜色代码**：`&6Text`、`&cText`、`&aText`
2. **颜色名称**：`&gold&Text`、`&red&Text`、`&green&Text`
3. **RGB十六进制**：`&#ff00ff&Text`、`&#f0f&Text`
4. **渐变色**：`&#f0f-#fff&Text`、`&#fff-#333-#222&Text`
5. **彩虹色**：`&rainbow&Text`、`&rainbow:20&Text`
6. **格式化**：`**粗体**`、`##斜体##`、`__下划线__`、`~~删除线~~`、`??混淆??`

### 智能颜色增强
```java
private String applyBasicMineDownColors(String text) {
    // 如果文本已经包含颜色代码，直接返回
    if (MineDownUtil.containsMineDownFormat(text)) {
        return text;
    }
    
    // 为普通死亡消息添加彩虹色效果
    return "&rainbow&" + text;
}
```

## 🔧 关键技术特性

### 1. 防重复机制
```java
// 生成唯一的死亡事件ID
String deathId = player.getName() + "_" + System.currentTimeMillis();

// 防重复处理机制
if (!processedDeaths.add(deathId)) {
    if (debugEnabled) {
        plugin.getLogger().info("跳过重复的死亡事件: " + player.getName());
    }
    return;
}
```

### 2. 智能广播逻辑
```java
// 避免向来源服务器重复发送
boolean includeSource = configManager.getBoolean("death_messages.include_source_server", false);

for (Player player : server.getAllPlayers()) {
    String playerServer = player.getCurrentServer()
        .map(conn -> conn.getServerInfo().getName())
        .orElse("unknown");
    
    boolean shouldSend = !playerServer.equals(sourceServer) || includeSource;
    
    if (shouldSend) {
        player.sendMessage(deathMessage);
    }
}
```

### 3. Team前缀保留
- ✅ **保留原有显示**：不强制移除team前缀，保持团队信息
- ✅ **颜色增强**：在保留team前缀的基础上增强颜色效果
- ✅ **武器悬停**：完整保留武器的悬停显示效果

## 📊 架构优势

### 1. 流程简化
- ✅ **单一职责**：Paper端负责解析，Velocity端负责广播
- ✅ **清晰流程**：四步工作流程，逻辑清晰
- ✅ **减少复杂性**：移除了复杂的死亡原因分析和映射

### 2. 性能优化
- ✅ **防重复机制**：避免同一死亡事件被多次处理
- ✅ **高效序列化**：使用Adventure的原生JSON序列化
- ✅ **内存管理**：自动清理处理记录，防止内存泄漏

### 3. 功能增强
- ✅ **MineDown集成**：完整支持所有MineDown颜色格式
- ✅ **智能增强**：自动检测并增强颜色效果
- ✅ **向后兼容**：支持原有的颜色代码和格式

### 4. 调试友好
- ✅ **详细日志**：提供完整的处理流程日志
- ✅ **错误处理**：优雅的异常处理和回退机制
- ✅ **配置灵活**：可以独立控制各个功能模块

## 📋 配置文件

### Velocity端配置
```yaml
# MessageTools 配置文件 (Velocity端)
death_messages:
  enabled: true
  broadcast_to_all_servers: true
  include_source_server: false  # 避免重复发送
  console_output: true

debug:
  enabled: true      # 查看处理日志
  verbose_events: true
```

### Paper端配置
```yaml
# MessageTools-Paper 配置文件
death_messages:
  enabled: true
  cancel_original: true  # 取消原始死亡消息

debug:
  enabled: true
  verbose_events: true
```

## 🎮 效果展示

### 死亡消息示例
#### 原始消息（带team前缀）
```
[Team红队]PlayerA 被 [Team蓝队]PlayerB 用 钻石剑 杀死了
```

#### MineDown增强后
```
[Team红队]PlayerA 被 [Team蓝队]PlayerB 用 钻石剑 杀死了
```
- 如果原始消息没有颜色代码，会自动添加彩虹色效果
- 如果原始消息已有颜色代码，保持原有效果
- 武器悬停效果完整保留

### 处理日志示例
#### Paper端日志
```
[INFO] 处理玩家死亡事件: PlayerA (ID: PlayerA_1703123456789)
[INFO] 构建的死亡消息JSON: {"text":"[Team红队]PlayerA 被..."}
[INFO] 完整死亡消息已成功发送到代理
```

#### Velocity端日志
```
[INFO] === 处理死亡消息 ===
[INFO] 玩家: PlayerA, 来源: server1
[INFO] 死亡消息广播完成 - 总玩家数: 3, 发送数: 2, 跳过数: 1
[INFO] [死亡消息] [Team红队]PlayerA 被 [Team蓝队]PlayerB 用 钻石剑 杀死了
[INFO] 死亡消息处理完成
```

## 📦 部署文件

### 最终JAR文件
- **Velocity端**：`MessageTools-1.7-SNAPSHOT.jar` (包含MineDown库)
- **Paper端**：`messagetools-paper-1.0-SNAPSHOT.jar` (包含MineDown库)

### 依赖管理
- ✅ **MineDown库已内置**：两个插件都已经包含了MineDown库
- ✅ **重定位处理**：避免与其他插件冲突
- ✅ **版本兼容**：使用最新的MineDown-adventure版本

## 🎯 总结

通过这次返璞归真的重构，MessageTools现在拥有了：

1. **完美的简化流程**：拦截→解析→发送→广播
2. **强大的MineDown支持**：完整的颜色格式支持
3. **智能的处理机制**：防重复、智能广播、错误处理
4. **优秀的性能表现**：高效序列化、内存管理
5. **出色的用户体验**：保留team前缀、武器悬停效果

MessageTools现在真正实现了你要求的简化工作流程，同时保持了强大的功能和出色的性能！🚀

**工作流程总结**：
玩家死亡 → Paper端拦截并解析MineDown颜色 → 发送JSON到Velocity → Velocity全服广播 → 玩家看到彩色死亡消息 ✨
